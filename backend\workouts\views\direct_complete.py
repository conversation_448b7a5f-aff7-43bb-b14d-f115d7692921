import logging
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from accounts.models import User<PERSON><PERSON><PERSON>le, UserScore, UserProgress
from workouts.models import WorkoutDay, WorkoutSession, WorkoutLog, WorkoutSessionLog, UserWorkoutPlan
from workouts.serializers import WorkoutSessionLogSerializer

logger = logging.getLogger(__name__)

@api_view(['POST', 'OPTIONS'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def direct_complete_workout_session(request):
    # Handle OPTIONS request for CORS preflight
    if request.method == 'OPTIONS':
        response = Response()
        response["Allow"] = "POST, OPTIONS"
        return response
    """Directly complete a workout session without requiring a session log ID"""
    logger.info("Processing direct workout session completion")
    user_profile = get_object_or_404(UserProfile, user=request.user)

    try:
        # Get the workout day from the request data
        # Support both parameter formats: workout_day_id and workout_day
        workout_day_id = request.data.get('workout_day_id') or request.data.get('workout_day')
        date_str = request.data.get('date')
        # Support both parameter formats: workout_session_id and workout_session
        workout_session_id = request.data.get('workout_session_id') or request.data.get('workout_session')

        # Log only essential information
        logger.info(f"User {request.user.username} completing workout - Day ID: {workout_day_id}, Session ID: {workout_session_id}, Date: {date_str}")

        # Parse the date
        if date_str:
            try:
                date_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                date_obj = timezone.now().date()
                logger.info(f"Invalid date format, using today: {date_obj}")
        else:
            date_obj = timezone.now().date()
            logger.info(f"No date provided, using today: {date_obj}")

        # Try multiple approaches to find the workout day
        workout_day = None

        # Approach 1: Try to find by ID
        if workout_day_id:
            try:
                # Convert to int if it's a string
                if isinstance(workout_day_id, str):
                    try:
                        workout_day_id = int(workout_day_id)
                    except ValueError:
                        logger.error(f"Could not convert workout_day_id '{workout_day_id}' to int")

                # Try to find the workout day by ID
                workout_day = WorkoutDay.objects.get(id=workout_day_id)
                logger.info(f"Found workout day by ID: {workout_day.id} - {workout_day.name}")
            except WorkoutDay.DoesNotExist:
                logger.error(f"Workout day with ID {workout_day_id} not found")

                # Log all workout days in the database for debugging
                all_workout_days = WorkoutDay.objects.all()
                logger.info(f"All workout days in database: {all_workout_days.count()}")
                for day in all_workout_days:
                    logger.info(f"  - Day ID: {day.id}, Name: {day.name}")

                # Check if workout session exists

                # Special handling for the case where workout_day_id is actually a workout_session_id
                # This happens when the frontend sends the wrong ID
                if workout_session_id and workout_session_id == workout_day_id:
                    logger.info(f"Attempting to find workout day using workout_session_id: {workout_session_id}")
                    try:
                        # Try to find the workout session by ID
                        workout_session = WorkoutSession.objects.get(id=workout_session_id)
                        # Get the workout day from the workout session
                        workout_day = workout_session.workout_day
                        logger.info(f"Found workout day via workout session: {workout_day.id} - {workout_day.name}")
                    except (WorkoutSession.DoesNotExist, AttributeError):
                        logger.error(f"Could not find workout day via workout session ID: {workout_session_id}")
                else:
                    # Try to find any workout session with this ID
                    try:
                        # Try to find the workout session by ID
                        workout_session = WorkoutSession.objects.get(id=workout_day_id)
                        # Get the workout day from the workout session
                        workout_day = workout_session.workout_day
                        logger.info(f"Found workout day via workout session ID {workout_day_id}: {workout_day.id} - {workout_day.name}")
                    except (WorkoutSession.DoesNotExist, AttributeError):
                        logger.error(f"Could not find workout day via workout session ID: {workout_day_id}")

        # Approach 2: If not found by ID, try to find by user and date
        if not workout_day:
            try:
                # Find workout logs for this user and date
                workout_logs = WorkoutLog.objects.filter(user=user_profile, date=date_obj)
                if workout_logs.exists():
                    # Use the workout day from the first log
                    workout_day = workout_logs.first().workout_day
                    logger.info(f"Found workout day from logs: {workout_day.id} - {workout_day.name}")
            except Exception as e:
                logger.error(f"Error finding workout day from logs: {e}")

        # Approach 3: If still not found, try to find any workout day for this user
        if not workout_day:
            try:
                # Find any workout day for this user
                user_workout_plans = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True)
                if user_workout_plans.exists():
                    workout_days = WorkoutDay.objects.filter(user_workout_plan__in=user_workout_plans)
                    if workout_days.exists():
                        workout_day = workout_days.first()
                        logger.info(f"Found workout day from user plans: {workout_day.id} - {workout_day.name}")
            except Exception as e:
                logger.error(f"Error finding workout day from user plans: {e}")

        # Approach 4: Last resort - create a new workout day for this user
        if not workout_day:
            try:
                # If we have a workout session ID, try to use that to create a new workout day
                if workout_session_id:
                    logger.info(f"Creating a new workout day for user {user_profile.id} based on workout session ID {workout_session_id}")
                else:
                    logger.info(f"Creating a new workout day for user {user_profile.id}")

                # Find or create a user workout plan
                user_workout_plan = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True).first()
                if not user_workout_plan:
                    # Create a new user workout plan if none exists
                    from workouts.models import WorkoutPlan
                    default_plan = WorkoutPlan.objects.first()
                    if not default_plan:
                        # Create a default workout plan if none exists
                        default_plan = WorkoutPlan.objects.create(
                            name="Default Workout Plan",
                            description="Auto-created workout plan",
                            difficulty="BEGINNER",
                            duration_weeks=4,
                            goal="GENERAL_FITNESS"
                        )

                    user_workout_plan = UserWorkoutPlan.objects.create(
                        user=user_profile,
                        workout_plan=default_plan,
                        start_date=timezone.now().date(),
                        is_active=True
                    )

                # Create a new workout day
                workout_day = WorkoutDay.objects.create(
                    user_workout_plan=user_workout_plan,
                    name=request.data.get('session_name', 'Auto-created Workout Day'),
                    day_number=1,
                    is_rest_day=False
                )

                # Create a workout session for this day
                workout_session = WorkoutSession.objects.create(
                    workout_day=workout_day,
                    user_workout_plan=user_workout_plan,
                    name=request.data.get('session_name', 'Auto-created Workout Session'),
                    order=1
                )

                logger.info(f"Created new workout day: {workout_day.id} - {workout_day.name}")
                logger.info(f"Created new workout session: {workout_session.id} - {workout_session.name}")
            except Exception as e:
                logger.error(f"Error creating workout day: {e}")
                return Response({"detail": "Could not create a workout day"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Parse the date from the request data
        date_str = request.data.get('date')

        if date_str:
            try:
                # Parse the date string to a date object
                if isinstance(date_str, str):
                    date_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
                else:
                    date_obj = date_str
            except ValueError:
                logger.error(f"Invalid date format: {date_str}")
                return Response({"detail": f"Invalid date format: {date_str}. Expected format: YYYY-MM-DD"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            date_obj = timezone.now().date()

        # Find the workout log for this user and date, regardless of workout day
        # This is more reliable than using the workout_day_id from the request
        try:
            # First, try to find an existing workout log for this date
            workout_logs = WorkoutLog.objects.filter(
                user=user_profile,
                date=date_obj
            )

            if workout_logs.exists():
                # Use the first log found for this date
                workout_log = workout_logs.first()
                logger.info(f"Found existing WorkoutLog for date {date_obj}: {workout_log.id}")

                # Update the workout day reference to ensure consistency
                if workout_log.workout_day.id != workout_day.id:
                    logger.info(f"Updating WorkoutLog workout_day from {workout_log.workout_day.id} to {workout_day.id}")
                    workout_log.workout_day = workout_day
                    workout_log.save(update_fields=['workout_day'])
            else:
                # Create a new workout log if none exists
                workout_log = WorkoutLog.objects.create(
                    user=user_profile,
                    workout_day=workout_day,
                    date=date_obj,
                    is_completed=True,
                    completion_time=timezone.now(),
                    calories_burned=request.data.get('calories_burned', 0),
                    duration_minutes=request.data.get('duration_minutes', 30),
                    notes=request.data.get('notes', '')
                )
                logger.info(f"Created new WorkoutLog for date {date_obj}: {workout_log.id}")
        except Exception as e:
            logger.error(f"Error finding/creating workout log: {e}")
            return Response({"detail": f"Error with workout log: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Update the workout log with the latest data
        # We always want to update these fields regardless of whether it was just created
        workout_log.is_completed = True
        workout_log.completion_time = timezone.now()
        workout_log.calories_burned = request.data.get('calories_burned', 0)
        workout_log.duration_minutes = request.data.get('duration_minutes', 30)
        workout_log.notes = request.data.get('notes', '')
        workout_log.save()

        # We already got the workout session ID earlier, just log it
        logger.info(f"Looking for workout session with ID: {workout_session_id}")

        # Try multiple approaches to find the workout session
        workout_session = None

        # Convert to int if it's a string
        if isinstance(workout_session_id, str) and workout_session_id:
            try:
                workout_session_id = int(workout_session_id)
                logger.info(f"Converted workout_session_id from string to int: {workout_session_id}")
            except ValueError:
                logger.error(f"Could not convert workout_session_id '{workout_session_id}' to int")
                # Continue with the string value

        # APPROACH 0: Find the user's active workout plan and get the session for today
        # This is the most reliable approach as it uses the user's actual plan
        try:
            # Find the user's active workout plan
            from django.db.models import Q
            user_workout_plan = UserWorkoutPlan.objects.filter(
                user=user_profile,
                is_active=True,
                start_date__lte=date_obj,  # Plan must have started by the target date
            ).filter(
                # Plan must not have ended before the target date OR end_date is null
                Q(end_date__gte=date_obj) | Q(end_date__isnull=True)
            ).order_by('-created_at').first()

            if user_workout_plan:
                logger.info(f"Found active workout plan for user: {user_workout_plan.id}")

                # Find the workout day for this date in the user's plan
                user_workout_day = WorkoutDay.objects.filter(
                    user_workout_plan=user_workout_plan,
                    date=date_obj
                ).first()

                if user_workout_day:
                    logger.info(f"Found workout day for date {date_obj}: {user_workout_day.id} - {user_workout_day.name}")

                    # Use this workout day instead of the one found earlier
                    workout_day = user_workout_day

                    # Find the session in this day by ID, order, or name
                    if workout_session_id:
                        # Try to find by exact ID first
                        user_workout_session = WorkoutSession.objects.filter(
                            workout_day=user_workout_day,
                            id=workout_session_id
                        ).first()

                        if user_workout_session:
                            workout_session = user_workout_session
                            logger.info(f"Found matching session by ID in user's workout day: {workout_session.id} - {workout_session.name}")

                    # If no session found by ID, check if we have an order parameter
                    session_order = request.data.get('session_order')
                    if not workout_session and session_order is not None:
                        try:
                            if isinstance(session_order, str):
                                session_order = int(session_order)

                            user_workout_session = WorkoutSession.objects.filter(
                                workout_day=user_workout_day,
                                order=session_order
                            ).first()

                            if user_workout_session:
                                workout_session = user_workout_session
                                logger.info(f"Found session by order {session_order} in user's workout day: {workout_session.id} - {workout_session.name}")
                        except (ValueError, TypeError):
                            logger.error(f"Invalid session_order value: {session_order}")

                    # If still no session found, check if we have a session name
                    session_name = request.data.get('session_name')
                    if not workout_session and session_name:
                        user_workout_session = WorkoutSession.objects.filter(
                            workout_day=user_workout_day,
                            name__iexact=session_name  # Case-insensitive match
                        ).first()

                        if user_workout_session:
                            workout_session = user_workout_session
                            logger.info(f"Found session by name '{session_name}' in user's workout day: {workout_session.id} - {workout_session.name}")

                    # If still no session found, get the first one as a last resort
                    if not workout_session:
                        # Log a warning since we're falling back to the first session
                        logger.warning(f"No specific session identified. Falling back to first session in workout day {user_workout_day.id}")

                        user_workout_session = WorkoutSession.objects.filter(
                            workout_day=user_workout_day
                        ).order_by('order').first()

                        if user_workout_session:
                            workout_session = user_workout_session
                            logger.info(f"Using first session from user's workout day: {workout_session.id} - {workout_session.name}")
        except Exception as e:
            logger.error(f"Error finding user's workout plan: {e}")

        # If we still don't have a workout session, try the other approaches

        # Approach 1: Try to find by ID and workout day, ensuring it belongs to the user's plan
        if not workout_session and workout_session_id and user_workout_plan:
            try:
                workout_session = WorkoutSession.objects.get(
                    id=workout_session_id,
                    workout_day=workout_day,
                    user_workout_plan=user_workout_plan  # Ensure it's the user's session
                )
                logger.info(f"Found user-specific workout session by ID and workout day: {workout_session.id} - {workout_session.name}")
            except WorkoutSession.DoesNotExist:
                logger.error(f"User-specific workout session with ID {workout_session_id} not found for workout day {workout_day.id}")
            except Exception as e:
                logger.error(f"Error finding user-specific workout session by ID and workout day: {e}")

        # Approach 2: If not found, try to find by ID only, but still ensure it belongs to the user
        if not workout_session and workout_session_id and user_workout_plan:
            try:
                workout_session = WorkoutSession.objects.get(
                    id=workout_session_id,
                    user_workout_plan=user_workout_plan  # Ensure it's the user's session
                )
                logger.info(f"Found user-specific workout session by ID only: {workout_session.id} - {workout_session.name}")
            except WorkoutSession.DoesNotExist:
                logger.error(f"User-specific workout session with ID {workout_session_id} not found at all")
            except Exception as e:
                logger.error(f"Error finding user-specific workout session by ID only: {e}")

        # Approach 3: If still not found, try to find any user-specific session for this workout day
        if not workout_session and workout_day and user_workout_plan:
            try:
                workout_session = WorkoutSession.objects.filter(
                    workout_day=workout_day,
                    user_workout_plan=user_workout_plan  # Ensure it's the user's session
                ).first()
                if workout_session:
                    logger.info(f"Found user-specific workout session from workout day: {workout_session.id} - {workout_session.name}")
                else:
                    logger.error(f"No user-specific workout sessions found for workout day {workout_day.id}")
            except Exception as e:
                logger.error(f"Error finding user-specific workout session from workout day: {e}")

        # Approach 4: Last resort - use any user-specific workout session
        if not workout_session and user_workout_plan:
            try:
                workout_session = WorkoutSession.objects.filter(
                    user_workout_plan=user_workout_plan  # Ensure it's the user's session
                ).first()
                if workout_session:
                    logger.info(f"Using fallback user-specific workout session: {workout_session.id} - {workout_session.name}")
                else:
                    logger.error(f"No user-specific workout sessions found for user {user_profile.id}")
                    return Response({"detail": "No workout sessions found for your account"}, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                logger.error(f"Error finding fallback user-specific workout session: {e}")
                return Response({"detail": "No workout sessions found for your account"}, status=status.HTTP_404_NOT_FOUND)

        # First, try to find an existing session log for this workout log and session
        try:
            # Look for a session log that matches our workout log and session
            session_log = WorkoutSessionLog.objects.filter(
                workout_log=workout_log,
                workout_session=workout_session
            ).first()

            if session_log:
                logger.info(f"Found existing session log: {session_log.id} for workout log {workout_log.id} and session {workout_session.id}")
                # Update the existing session log
                session_log.is_completed = True
                session_log.completion_time = timezone.now()
                session_log.calories_burned = request.data.get('calories_burned', 0)
                session_log.duration_minutes = request.data.get('duration_minutes', 30)
                session_log.notes = request.data.get('notes', '')
                session_log.save()
            else:
                # Create a new session log
                session_log = WorkoutSessionLog.objects.create(
                    workout_log=workout_log,
                    workout_session=workout_session,
                    date=date_obj,
                    is_completed=True,
                    completion_time=timezone.now(),
                    calories_burned=request.data.get('calories_burned', 0),
                    duration_minutes=request.data.get('duration_minutes', 30),
                    notes=request.data.get('notes', '')
                )
                logger.info(f"Created new session log: {session_log.id} for workout log {workout_log.id} and session {workout_session.id}")
        except Exception as e:
            logger.error(f"Error finding/creating session log: {e}")
            return Response({"detail": f"Error with session log: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Check if all sessions for this workout day are now completed
        try:
            # Get ALL sessions for this workout day (both template and user-specific)
            all_sessions = WorkoutSession.objects.filter(
                workout_day=workout_day
            )
            all_session_count = all_sessions.count()

            # Log all sessions for debugging
            logger.info(f"All sessions for workout day {workout_day.id}:")
            for s in all_sessions:
                logger.info(f"  - Session ID: {s.id}, Name: {s.name}, User Plan: {s.user_workout_plan_id}")

            # Get all completed session logs for this workout log
            # Count ALL completed session logs, regardless of whether they're for template or user-specific sessions
            completed_session_logs = WorkoutSessionLog.objects.filter(
                workout_log=workout_log,
                is_completed=True
            )
            completed_session_count = completed_session_logs.count()

            logger.info(f"Completed sessions: {completed_session_count}/{all_session_count} for user {user_profile.id}, workout day {workout_day.id} on {date_obj}")

            # If all sessions are completed, mark the workout log as completed
            if completed_session_count >= all_session_count and all_session_count > 0:
                logger.info(f"All sessions completed for user {user_profile.id}, workout day {workout_day.id} on {date_obj}. Marking workout log {workout_log.id} as completed.")

                # Calculate total calories and duration from all session logs
                total_calories = 0
                total_duration = 0
                for session_log in completed_session_logs:
                    total_calories += session_log.calories_burned or 0
                    total_duration += session_log.duration_minutes or 0

                logger.info(f"Total calories: {total_calories}, Total duration: {total_duration}")

                # Update the workout log with completion info and totals
                workout_log.is_completed = True
                workout_log.completion_time = timezone.now()
                workout_log.calories_burned = total_calories
                workout_log.duration_minutes = total_duration
                workout_log.save(update_fields=['is_completed', 'completion_time', 'calories_burned', 'duration_minutes'])

                # Log the workout log status
                logger.info(f"Workout log {workout_log.id} is now marked as completed: {workout_log.is_completed}, calories: {workout_log.calories_burned}, duration: {workout_log.duration_minutes}")
        except Exception as e:
            logger.error(f"Error checking session completion status: {e}")

        # Update user progress
        progress, _ = UserProgress.objects.get_or_create(user=user_profile)
        progress.total_workouts_completed = (progress.total_workouts_completed or 0) + 1
        progress.save(update_fields=['total_workouts_completed'])

        # Update streak
        try:
            progress.update_streak(date_obj)  # Pass the date to update_streak
        except Exception as e:
            logger.error(f"Error updating streak: {e}", exc_info=True)

        # Update score directly - 2 points per workout session
        score, _ = UserScore.objects.get_or_create(user=user_profile)

        # Add 2 points for workout session completion, max 30
        score.workout_score = min(30, (score.workout_score or 0) + 2)

        # Update total score
        score.total_score = min(100, (
            (score.workout_score or 0) +
            (score.streak_score or 0) +
            (score.nutrition_score or 0) +
            (score.goal_score or 0)
        ))

        # Update total points earned
        score.total_points_earned = (score.total_points_earned or 0) + 2
        score.last_calculated = timezone.now()
        score.save()

        # Log the activity
        try:
            from activity_logs.utils import log_workout_session_completion
            log_workout_session_completion(user_profile, session_log)
        except Exception as e:
            logger.error(f"Error logging workout completion activity: {e}", exc_info=True)

        serializer = WorkoutSessionLogSerializer(session_log)
        response_data = serializer.data

        # Log completion success
        logger.info(f"Workout session completed successfully - Session: {workout_session.id}, Log: {session_log.id}")

        return Response(response_data)

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error in direct_complete: {error_msg}", exc_info=True)

        # Create a detailed error response
        error_response = {
            "detail": f"Error: {error_msg}",
            "error_type": type(e).__name__,
            "timestamp": timezone.now().isoformat()
        }

        return Response(error_response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
