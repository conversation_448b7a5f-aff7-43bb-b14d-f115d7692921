import logging
import os
from django.db import models as db_models # Alias to avoid conflict
from django.db.models import Q, Sum # Import Q and Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth import authenticate, login, get_user_model
from django.forms import ValidationError
from django.utils.decorators import method_decorator
from django.views.decorators.debug import sensitive_post_parameters
from django.views.decorators.csrf import ensure_csrf_cookie, csrf_protect # Import csrf_protect
from django.utils.http import urlsafe_base64_decode
from django.http import JsonResponse, Http404 # Import JsonResponse and Http404
from django.middleware.csrf import get_token # Import get_token
from django.utils.encoding import force_str, force_bytes # Import force_str, force_bytes
from django.template.loader import render_to_string # Import render_to_string
from django.core.mail import send_mail # Import send_mail
from django.conf import settings # Import settings
from django.db import transaction # Import transaction


from rest_framework import generics, permissions, status, viewsets, serializers
from rest_framework.authentication import TokenAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser
from rest_framework.exceptions import NotFound, APIException, PermissionDenied

from django_filters import rest_framework as filters

from dj_rest_auth.registration.views import SocialLoginView

from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.facebook.views import FacebookOAuth2Adapter
from allauth.socialaccount.providers.apple.views import AppleOAuth2Adapter
from allauth.socialaccount.providers.oauth2.client import OAuth2Client

from datetime import datetime, timedelta
import uuid
import json
import pytz # Add import

# Handle the stripe import using environment variable
if os.environ.get('MOCK_STRIPE'):
    class MockStripe:
        class Customer:
            @staticmethod
            def create(*args, **kwargs): return {"id": "cus_mock123456789"}
            @staticmethod
            def retrieve(customer_id): return {"id": customer_id}
        class Subscription:
            @staticmethod
            def create(*args, **kwargs): return {"id": "sub_mock123456789"}
        class PaymentMethod:
            @staticmethod
            def attach(*args, **kwargs): return {"id": "pm_mock123456789"}
        class Checkout:
            class Session:
                @staticmethod
                def create(*args, **kwargs): return {"id": "cs_mock123456789", "url": "https://example.com/checkout"}
        api_key = None
    stripe = MockStripe()
    logger = logging.getLogger(__name__)
    logger.info("Using mock stripe module")
else:
    try: import stripe
    except ImportError:
        logger = logging.getLogger(__name__)
        logger.error("Failed to import stripe module")
        class MinimalMockStripe: api_key = None
        stripe = MinimalMockStripe()


from rest_framework_simplejwt.tokens import RefreshToken

from .models import (
    User, UserProfile, UserProgress,
    UserScore, DailyScore
)
from .serializers import (
    UserSerializer, UserProfileSerializer, UserProgressSerializer,
    UserScoreSerializer, DetailedScoreSerializer
)
# Need models for cloning and plan selection
from workouts.models import (
    WorkoutPlan, UserWorkoutPlan, WorkoutLog, WorkoutSchedule,
    WorkoutDay, WorkoutSession, WorkoutSection, ExerciseInstance # Added for cloning
)
# Need WorkoutPlanSerializer for response data
from workouts.serializers import WorkoutPlanSerializer
from meals.models import (
    MealPlan, UserMealPlan, DailyMealPlan, MealLog, MealSchedule # Add MealSchedule
)
from meals.serializers import MealPlanSerializer # Keep needed import
# Removed filters import as WorkoutPlanFilter was moved
# from django_filters import rest_framework as filters

from .forms import CustomSetPasswordForm, CustomPasswordResetForm

# Set up logging
logger = logging.getLogger(__name__)

User = get_user_model()

AGE_MAPPING = {
    'UNDER_30': 25,
    '30_40': 35,
    '40_PLUS': 45
}

# Helper function for plan selection (Best Fit Approach)
def select_plans(profile):
    logger.info(f"Plan Matching (Best Fit) - Profile: {profile.user.username}")
    logger.info(f"Profile Details: Goal={profile.fitness_goal}, Level={profile.fitness_level}, Gender={profile.gender}, AgeGroup={profile.age_group}, Location={profile.workout_location}, WorkoutDays={profile.workout_days}, HasHomeEquip={profile.has_home_equipment}, HealthConditions={profile.health_conditions}, IsKeto={profile.is_keto}, IsIntermittentFasting={profile.is_intermittent_fasting}")

    # Define weights for criteria (higher weight = more important)
    # Critical (10 points): health_conditions, gender (if specific match).
    # High (5 points): fitness_goal/goal, age_group.
    # Medium (3 points): fitness_level, workout_location, is_keto, is_intermittent_fasting.
    # Low (1 point): workout_days, has_home_equipment.
    
    # Process user's health conditions once
    user_conditions = frozenset()
    try:
        profile_conditions_raw = profile.health_conditions or []
        flat_profile_conditions = []
        if isinstance(profile_conditions_raw, str):
            try:
                parsed = json.loads(profile_conditions_raw.replace("'", '"'))
                if isinstance(parsed, list): flat_profile_conditions.extend(parsed)
                else: flat_profile_conditions.append(profile_conditions_raw)
            except json.JSONDecodeError: flat_profile_conditions.append(profile_conditions_raw)
        elif isinstance(profile_conditions_raw, list): flat_profile_conditions.extend(profile_conditions_raw)
        else: flat_profile_conditions.append(profile_conditions_raw)
        user_conditions = frozenset(str(hc).strip() for hc in flat_profile_conditions if hc and str(hc).strip() != 'NONE')
    except Exception as e:
        logger.error(f"Error processing user health conditions '{profile.health_conditions}': {e}", exc_info=True)
    logger.info(f"User's processed health conditions: {user_conditions}")

    def calculate_plan_score(plan, is_workout_plan):
        score = 0
        
        # Process plan's allowed health conditions
        plan_allowed_conditions = frozenset()
        try:
            allowed_conditions_raw = plan.health_conditions_allowed or []
            flat_allowed_conditions = []
            if isinstance(allowed_conditions_raw, str):
                try:
                    parsed = json.loads(allowed_conditions_raw.replace("'", '"'))
                    if isinstance(parsed, list): flat_allowed_conditions.extend(parsed)
                    else: flat_allowed_conditions.append(allowed_conditions_raw)
                except json.JSONDecodeError: flat_allowed_conditions.append(allowed_conditions_raw)
            elif isinstance(allowed_conditions_raw, list): flat_allowed_conditions.extend(allowed_conditions_raw)
            else: flat_allowed_conditions.append(allowed_conditions_raw)
            plan_allowed_conditions = frozenset(str(hc).strip() for hc in flat_allowed_conditions if hc and str(hc).strip() != 'NONE')
        except Exception as e:
            logger.error(f"Error processing plan {plan.id} allowed conditions '{plan.health_conditions_allowed}': {e}", exc_info=True)

        # CRITICAL: Health Conditions Match (10 points)
        # If user has conditions: user_conditions must be a subset of plan_allowed_conditions.
        # If user has NO conditions: plan_allowed_conditions must contain 'NONE' or be empty.
        if user_conditions:
            if user_conditions.issubset(plan_allowed_conditions):
                score += 10
                logger.debug(f"Plan {plan.name}: Health conditions match (user conditions are subset of plan's). +10 points.")
            else:
                logger.debug(f"Plan {plan.name}: Health conditions MISMATCH (user conditions not subset). Disqualifying.")
                return -1 # Disqualify the plan
        elif 'NONE' in plan_allowed_conditions or not plan_allowed_conditions: # User has no conditions, plan allows 'NONE' or has no specific restrictions
            score += 10
            logger.debug(f"Plan {plan.name}: Health conditions match (user has none, plan allows none). +10 points.")
        else: # User has no conditions, but plan requires specific conditions (e.g., only for DIABETES users)
            logger.debug(f"Plan {plan.name}: Health conditions MISMATCH (user has none, plan requires specific conditions). Disqualifying.")
            return -1 # Disqualify

        # Gender (Critical - 10 points for exact match, 5 for unisex/any match)
        if profile.gender and plan.gender:
            if profile.gender == plan.gender: # Exact match (M to M, F to F)
                score += 10
                logger.debug(f"Plan {plan.name}: Gender exact match ({profile.gender} to {plan.gender}). +10 points.")
            elif profile.gender == 'O' and plan.gender in ['M', 'F', 'UNISEX', 'ANY', None, '']: # User is 'Other', matches any plan
                score += 5
                logger.debug(f"Plan {plan.name}: Gender general match (user 'O', plan {plan.gender}). +5 points.")
            elif plan.gender in ['UNISEX', 'ANY', None, '']: # Plan is unisex/general, matches any specific user gender
                score += 5
                logger.debug(f"Plan {plan.name}: Gender general match (plan {plan.gender}, user {profile.gender}). +5 points.")
            else:
                logger.debug(f"Plan {plan.name}: Gender mismatch ({profile.gender} vs {plan.gender}). Disqualifying.")
                return -1 # Disqualify if specific gender mismatch
        elif not profile.gender and plan.gender in ['UNISEX', 'ANY', None, '']: # User has no gender preference, plan is general
            score += 5
            logger.debug(f"Plan {plan.name}: Gender general match (user has none, plan is unisex/any). +5 points.")
        elif profile.gender and not plan.gender: # User has gender, plan has no preference (implicitly unisex)
            score += 5
            logger.debug(f"Plan {plan.name}: Gender general match (user has gender, plan has none). +5 points.")
        else: # Both profile.gender and plan.gender are None/empty
            score += 1 # Small bonus for no preference/no restriction
            logger.debug(f"Plan {plan.name}: Gender no preference/restriction. +1 point.")


        # Age Group (High - 5 points)
        if profile.age_group and plan.age_group:
            # plan.age_group can be a list or string. Convert to list for consistent check.
            plan_age_groups = [plan.age_group] if isinstance(plan.age_group, str) else (plan.age_group or [])
            if profile.age_group in plan_age_groups or 'ANY' in plan_age_groups:
                score += 5
                logger.debug(f"Plan {plan.name}: Age group match ({profile.age_group} in {plan_age_groups}). +5 points.")

        # Fitness Goal / Goal (High - 5 points)
        # Note: profile.fitness_goal is a single string (e.g., 'MUSCLE_GAIN')
        # plan.goal is also a single string (e.g., 'MUSCLE_GAIN')
        if profile.fitness_goal and plan.goal == profile.fitness_goal:
            score += 5
            logger.debug(f"Plan {plan.name}: Goal match ({profile.fitness_goal}). +5 points.")

        # Fitness Level (Medium - 3 points)
        if is_workout_plan and profile.fitness_level and plan.fitness_level == profile.fitness_level:
            score += 3
            logger.debug(f"Plan {plan.name}: Fitness level match. +3 points.")

        # Workout Location (Medium - 3 points)
        if is_workout_plan and profile.workout_location and plan.location:
            # plan.location can be a list or string. Convert to list for consistent check.
            plan_locations = [plan.location] if isinstance(plan.location, str) else (plan.location or [])
            if profile.workout_location in plan_locations or 'ANY' in plan_locations:
                score += 3
                logger.debug(f"Plan {plan.name}: Workout location match ({profile.workout_location} in {plan_locations}). +3 points.")

        # Dietary Preferences (Medium - 3 points for each)
        if not is_workout_plan: # Meal Plan specific
            if profile.is_keto is not None and plan.is_keto == profile.is_keto:
                score += 3
                logger.debug(f"Plan {plan.name}: Keto preference match. +3 points.")
            if profile.is_intermittent_fasting is not None and plan.is_intermittent_fasting == profile.is_intermittent_fasting:
                score += 3
                logger.debug(f"Plan {plan.name}: Intermittent fasting preference match. +3 points.")

        # Workout Days (Low - 1 point)
        if is_workout_plan and profile.workout_days and plan.workout_days:
            # plan.workout_days can be a list or string. Convert to list for consistent check.
            plan_workout_days = [plan.workout_days] if isinstance(plan.workout_days, str) else (plan.workout_days or [])
            if profile.workout_days in plan_workout_days:
                score += 1
                logger.debug(f"Plan {plan.name}: Workout days match ({profile.workout_days} in {plan_workout_days}). +1 point.")

        # Has Home Equipment (Low - 1 point)
        if is_workout_plan and profile.has_home_equipment is not None:
            if profile.has_home_equipment == plan.requires_equipment: # Match if both true or both false
                score += 1
                logger.debug(f"Plan {plan.name}: Home equipment match ({profile.has_home_equipment} == {plan.requires_equipment}). +1 point.")
        
        logger.debug(f"Plan {plan.name} final score: {score}")
        return score

    best_workout_plan = None
    max_workout_score = -1 # Initialize with a value that disqualifies non-matches

    # Iterate through all workout plans to find the best fit
    for wp in WorkoutPlan.objects.all():
        current_score = calculate_plan_score(wp, is_workout_plan=True)
        if current_score > max_workout_score:
            max_workout_score = current_score
            best_workout_plan = wp
        elif current_score == max_workout_score and current_score > -1:
            # Tie-breaking: Prefer plans with more specific criteria (e.g., not 'ANY' or 'UNISEX')
            # This is complex to implement generically. For now, keep the first one found.
            pass 

    best_meal_plan = None
    max_meal_score = -1 # Initialize with a value that disqualifies non-matches

    # Iterate through all meal plans to find the best fit
    for mp in MealPlan.objects.all():
        current_score = calculate_plan_score(mp, is_workout_plan=False)
        if current_score > max_meal_score:
            max_meal_score = current_score
            best_meal_plan = mp
        elif current_score == max_meal_score and current_score > -1:
            # Tie-breaking: Keep the first one found.
            pass

    logger.info(f"Plan Matching (Best Fit) - Selected Workout Plan: {best_workout_plan.name if best_workout_plan else 'None'} (Score: {max_workout_score})")
    logger.info(f"Plan Matching (Best Fit) - Selected Meal Plan: {best_meal_plan.name if best_meal_plan else 'None'} (Score: {max_meal_score})")

    return best_workout_plan, best_meal_plan

# Import the deep_clone_workout_plan_for_user function from workouts.models
from workouts.models import deep_clone_workout_plan_for_user

# Helper function for deep cloning meal plan structure
def deep_clone_meal_plan_for_user(user_mp):
    logger.info(f"Starting deep clone for UserMealPlan {user_mp.id}")
    global_plan = user_mp.meal_plan
    if not global_plan:
        logger.error(f"Cannot clone for UserMealPlan {user_mp.id}: Global MealPlan link is missing.")
        return
    if not user_mp.start_date:
        logger.error(f"Cannot clone for UserMealPlan {user_mp.id}: UserMealPlan start_date is missing.")
        return # Cannot calculate dates without a start date

    # First, delete any existing daily meal plans for this user to avoid duplicates
    existing_plans = DailyMealPlan.objects.filter(user_meal_plan=user_mp)
    if existing_plans.exists():
        count = existing_plans.count()
        existing_plans.delete()
        logger.info(f"Deleted {count} existing DailyMealPlans for UserMealPlan {user_mp.id}")

    # Also delete any existing meal schedules
    existing_schedules = MealSchedule.objects.filter(user_meal_plan=user_mp)
    if existing_schedules.exists():
        count = existing_schedules.count()
        existing_schedules.delete()
        logger.info(f"Deleted {count} existing MealSchedules for UserMealPlan {user_mp.id}")

    cloned_daily_plans_map = {} # Map original DailyMealPlan ID to cloned instance

    # --- Cloning Step ---
    # 1. Clone DailyMealPlans
    template_daily_plans = DailyMealPlan.objects.filter(meal_plan=global_plan, user_meal_plan__isnull=True)
    logger.info(f"Found {template_daily_plans.count()} template DailyMealPlans for Plan {global_plan.id}")

    daily_plans_to_create = []
    # Ensure start_date is a date object
    start_date = user_mp.start_date
    if isinstance(start_date, datetime):
        start_date = start_date.date()

    # Use the day_number from the template for date calculation
    for daily_plan_template in template_daily_plans.order_by('day_number'):
        original_daily_plan_id = daily_plan_template.id
        # Calculate the specific date for this daily plan
        # Ensure template day_number starts from 1 for correct timedelta
        day_offset = daily_plan_template.day_number - 1
        if day_offset < 0:
             logger.warning(f"Template DailyMealPlan ID {original_daily_plan_id} has non-positive day_number ({daily_plan_template.day_number}). Skipping.")
             continue
        target_date = start_date + timedelta(days=day_offset)

        daily_plans_to_create.append(
            DailyMealPlan(
                user_meal_plan=user_mp,
                meal_plan=None,
                name=daily_plan_template.name,
                day_of_week=daily_plan_template.day_of_week,
                day_number=daily_plan_template.day_number, # Use template's day_number
                date=target_date, # <<< SET THE DATE FIELD
                breakfast=daily_plan_template.breakfast,
                lunch=daily_plan_template.lunch,
                dinner=daily_plan_template.dinner
            )
        )
        logger.info(f"Prepared to clone DailyMealPlan {original_daily_plan_id} with date {target_date} for UserMealPlan {user_mp.id}")

    # Bulk create all daily plans at once
    if daily_plans_to_create:
        cloned_daily_plans = DailyMealPlan.objects.bulk_create(daily_plans_to_create)
        logger.info(f"Bulk created {len(cloned_daily_plans)} DailyMealPlans for UserMealPlan {user_mp.id}")

        # Map original daily plan IDs to cloned instances
        for i, daily_plan_template in enumerate(template_daily_plans.order_by('day_number')):
            if i < len(cloned_daily_plans):
                cloned_daily_plans_map[daily_plan_template.id] = cloned_daily_plans[i]
                logger.info(f"Mapped DailyMealPlan {daily_plan_template.id} to {cloned_daily_plans[i].id} for UserMealPlan {user_mp.id}")
        # Note: Meal FKs remain linked to global Meal objects

    # 2. Clone MealSchedules, linking to *cloned* DailyMealPlans
    template_schedules = MealSchedule.objects.filter(meal_plan=global_plan, user_meal_plan__isnull=True)
    logger.info(f"Found {template_schedules.count()} template MealSchedules for Plan {global_plan.id}")
    schedules_to_create = []
    for schedule_template in template_schedules:
        schedules_to_create.append(
            MealSchedule(
                user_meal_plan=user_mp,
                meal_plan=schedule_template.meal_plan, # Keep link to global plan
                week_number=schedule_template.week_number,
                monday=cloned_daily_plans_map.get(schedule_template.monday_id),
                tuesday=cloned_daily_plans_map.get(schedule_template.tuesday_id),
                wednesday=cloned_daily_plans_map.get(schedule_template.wednesday_id),
                thursday=cloned_daily_plans_map.get(schedule_template.thursday_id),
                friday=cloned_daily_plans_map.get(schedule_template.friday_id),
                saturday=cloned_daily_plans_map.get(schedule_template.saturday_id),
                sunday=cloned_daily_plans_map.get(schedule_template.sunday_id)
            )
        )
    if schedules_to_create:
        created_schedules = MealSchedule.objects.bulk_create(schedules_to_create)
        logger.info(f"Bulk created {len(schedules_to_create)} MealSchedules for UserMealPlan {user_mp.id}")

        # Now, update each schedule with day number mapping
        for schedule in created_schedules:
            # Create a mapping of day numbers to daily meal plan IDs
            days_mapping = {}

            # Calculate the base day number for this week
            base_day = (schedule.week_number - 1) * 7 + 1

            # Map weekday fields to day numbers
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            for i, field_name in enumerate(weekday_fields):
                day_number = base_day + i
                daily_plan = getattr(schedule, field_name)
                if daily_plan:
                    days_mapping[str(day_number)] = daily_plan.id

                    # For the first week, ensure day 1 is mapped to the first meal day
                    if schedule.week_number == 1 and i == 0:
                        # Explicitly map day 1 to the first meal day
                        days_mapping['1'] = daily_plan.id
                        logger.info(f"Explicitly mapped day 1 to meal day {daily_plan.id}")

            # Also add mapping by day-in-week (1-7) for backward compatibility
            for i, field_name in enumerate(weekday_fields):
                day_in_week = i + 1
                daily_plan = getattr(schedule, field_name)
                if daily_plan and str(day_in_week) not in days_mapping:
                    days_mapping[str(day_in_week)] = daily_plan.id

            # Update the schedule with the days mapping
            if days_mapping:
                schedule.days = days_mapping
                schedule.save(update_fields=['days'])
                logger.info(f"Updated MealSchedule {schedule.id} with days mapping: {days_mapping}")

    logger.info(f"Finished deep clone for UserMealPlan {user_mp.id}")


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def get_permissions(self):
        if self.action == 'create': return [permissions.AllowAny()]
        if self.action in ['list', 'destroy']: return [permissions.IsAdminUser()]
        if self.action in ['retrieve', 'update', 'partial_update', 'me', 'change_password']: return [permissions.IsAuthenticated()]
        return [permissions.IsAdminUser()]

    def perform_create(self, serializer):
        user = serializer.save()
        # Signal handler creates related objects

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def change_password(self, request, pk=None):
        user = self.get_object()
        if user != request.user and not request.user.is_staff:
             return Response({'error': 'Permission denied.'}, status=status.HTTP_403_FORBIDDEN)
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')
        if not old_password or not new_password:
             return Response({'error': 'Old and new passwords are required.'}, status=status.HTTP_400_BAD_REQUEST)
        if not user.check_password(old_password):
            return Response({'error': 'Wrong old password.'}, status=status.HTTP_400_BAD_REQUEST)
        user.set_password(new_password)
        user.save()
        return Response({'status': 'password set'})

# WorkoutPlanFilter moved to workouts/views/workout.py
# WorkoutPlanViewSet moved to workouts/views/workout.py
# WorkoutDayViewSet moved to workouts/views/workout.py
# WorkoutSectionViewSet moved to workouts/views/workout.py

class UserProfileViewSet(viewsets.ModelViewSet):
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserProfile.objects.filter(user=self.request.user)

    @action(detail=False, methods=['get'], url_path='me', url_name='me')
    def me(self, request):
        logger.info(f"UserProfileViewSet.me called for user: {request.user.id}")
        try:
            profile = get_object_or_404(UserProfile, user=request.user)
            logger.info(f"Found profile {profile.id} for user {request.user.id}")
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        except Http404:
             logger.warning(f"UserProfile not found for user {request.user.id} in UserProfileViewSet.me")
             return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
             logger.error(f"Error in UserProfileViewSet.me for user {request.user.id}: {e}", exc_info=True)
             return Response({"detail": "Error retrieving profile."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='questionnaire', url_name='questionnaire')
    @transaction.atomic # Wrap the entire questionnaire process in a transaction
    def questionnaire(self, request):
        """
        Process questionnaire submission and assign workout and meal plans.

        Parameters:
        - request: The HTTP request object
            - local_date: Optional. The client's local date in 'YYYY-MM-DD' format.
                         If provided, this date will be used as the start date for workout and meal plans
                         instead of the server's date. This helps ensure plans start on the correct day
                         in the user's local timezone.
        """
        logger.info(f"Questionnaire submission for user: {request.user.id}")
        logger.info(f"Raw request data: {request.data}")
        profile = get_object_or_404(UserProfile, user=request.user)
        data = request.data.copy()

        # Prepare data for the serializer, EXCLUDING health_conditions
        serializer_data = {}
        bool_map = {'YES': True, 'NO': False}
        bool_fields = ['is_keto', 'is_intermittent_fasting', 'is_physically_active', 'has_home_equipment']
        direct_map_fields = ['gender', 'fitness_goal', 'age_group', 'age', 'height', 'weight',
                             'cooking_preference', 'water_intake', 'workout_location',
                             'workout_days', 'fitness_level']

        # Explicitly handle fitness_goal mapping if request uses different key
        if 'primary_fitness_goal' in data:
             serializer_data['fitness_goal'] = data['primary_fitness_goal']
             logger.info(f"Mapping primary_fitness_goal '{data['primary_fitness_goal']}' to fitness_goal")
        elif 'fitness_goal' in data:
             serializer_data['fitness_goal'] = data['fitness_goal'] # Handle if key is already correct

        for field in bool_fields:
            if field in data and hasattr(profile, field):
                value = data[field]
                # Handle both boolean and string values
                if isinstance(value, bool):
                    serializer_data[field] = value
                elif isinstance(value, str):
                    serializer_data[field] = bool_map.get(value.upper())
                else:
                    logger.warning(f"Unexpected type for boolean field {field}: {type(value)} - {value}")
                    serializer_data[field] = bool(value)
        for field in direct_map_fields:
            # Skip fitness_goal if already handled
            if field != 'fitness_goal' and field in data and hasattr(profile, field):
                 serializer_data[field] = data[field]

        logger.info(f"Prepared serializer_data: {serializer_data}")

        # Validate and save other fields first
        serializer = UserProfileSerializer(profile, data=serializer_data, partial=True)
        if not serializer.is_valid():
             logger.warning(f"Questionnaire validation failed (main fields) for user {request.user.id}: {serializer.errors}")
             return Response({'success': False, 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        try:
            updated_profile = serializer.save() # Save validated fields
            logger.info(f"After serializer.save() - Age: {updated_profile.age}, Height: {updated_profile.height}, Weight: {updated_profile.weight}")

            # Now, process and set health_conditions directly on the model instance
            health_conditions_input = data.get('health_conditions')
            if isinstance(health_conditions_input, list):
                valid_conditions = [hc for hc in health_conditions_input if hc and hc != 'NONE']
                updated_profile.health_conditions = valid_conditions if valid_conditions else ['NONE']
            elif isinstance(health_conditions_input, str):
                updated_profile.health_conditions = [health_conditions_input] if health_conditions_input and health_conditions_input != 'NONE' else ['NONE']
            else:
                 updated_profile.health_conditions = ['NONE'] # Default

            fields_to_update = ['health_conditions']
            if 'age' in serializer_data: fields_to_update.append('age')
            if 'height' in serializer_data: fields_to_update.append('height')
            if 'weight' in serializer_data: fields_to_update.append('weight')
            if 'weight' in fields_to_update and not profile.initial_weight:
                 if hasattr(updated_profile, 'weight') and updated_profile.weight is not None:
                     updated_profile.initial_weight = updated_profile.weight
                     fields_to_update.append('initial_weight')
                 else:
                      logger.warning(f"Cannot set initial_weight for profile {updated_profile.id} because weight is missing after serializer save.")

            updated_profile.save(update_fields=fields_to_update)

        except Exception as e:
             logger.error(f"Error saving profile for user {request.user.id} after questionnaire: {e}", exc_info=True)
             return Response({'success': False, 'message': 'Error saving profile data.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # --- Plan Matching & Assignment ---
        try:
            workout_plan, meal_plan = select_plans(updated_profile)

            # --- Pre-deletion of old cloned items ---
            # Find any existing UserWorkoutPlan for the *selected* global plan
            existing_user_wp = UserWorkoutPlan.objects.filter(user=updated_profile, workout_plan=workout_plan).first() if workout_plan else None
            if existing_user_wp:
                logger.info(f"Deleting old cloned workout components for UserWorkoutPlan {existing_user_wp.id}")
                ExerciseInstance.objects.filter(user_workout_plan=existing_user_wp).delete()
                WorkoutSection.objects.filter(user_workout_plan=existing_user_wp).delete()
                WorkoutDay.objects.filter(user_workout_plan=existing_user_wp).delete()
                WorkoutSchedule.objects.filter(user_workout_plan=existing_user_wp).delete()

            # Find any existing UserMealPlan for the *selected* global plan
            existing_user_mp = UserMealPlan.objects.filter(user=updated_profile, meal_plan=meal_plan).first() if meal_plan else None
            if existing_user_mp:
                logger.info(f"Deleting old cloned meal components for UserMealPlan {existing_user_mp.id}")
                DailyMealPlan.objects.filter(user_meal_plan=existing_user_mp).delete()
                MealSchedule.objects.filter(user_meal_plan=existing_user_mp).delete()
            # --- End Pre-deletion ---

            # We'll deactivate all active plans in the specific plan assignment sections below

            # --- Workout Plan Assignment & Cloning ---
            user_wp = None # Initialize
            if workout_plan:
                logger.info(f"Plan Assignment - Attempting to assign WorkoutPlan ID {workout_plan.id} to UserProfile {updated_profile.id}")
                try:
                    # First, deactivate all existing workout plans for this user
                    # This ensures we don't have multiple active plans
                    UserWorkoutPlan.objects.filter(user=updated_profile, is_active=True).update(is_active=False)
                    logger.info(f"Deactivated all existing workout plans for UserProfile {updated_profile.id}")

                    # Get the date for the start_date (use client's local date if provided)
                    local_date = request.data.get('local_date')
                    if local_date:
                        try:
                            # Parse the local_date string to a date object
                            today = datetime.strptime(local_date, '%Y-%m-%d').date()
                            logger.info(f"Using client's local date: {today} for workout plan start date")
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid local_date format: {local_date}. Using server date instead.")
                            today = timezone.now().date()
                    else:
                        today = timezone.now().date()
                        logger.info(f"No local_date provided. Using server date: {today} for workout plan start date")

                    # Create a new workout plan with the appropriate date as the start date
                    user_wp = UserWorkoutPlan.objects.create(
                        user=updated_profile,
                        workout_plan=workout_plan,
                        start_date=today,
                        is_active=True,
                        is_modified=False,
                        modifications={}
                    )
                    logger.info(f"Plan Assignment - Created new UserWorkoutPlan (ID: {user_wp.id}, Start Date: {today}) for UserProfile {updated_profile.id}")

                    # Call deep cloning after creating the plan
                    deep_clone_workout_plan_for_user(user_wp)
                except Exception as wp_assign_error:
                    logger.error(f"Plan Assignment - FAILED to assign/clone WorkoutPlan ID {workout_plan.id} to UserProfile {updated_profile.id}: {wp_assign_error}", exc_info=True)
                    user_wp = None

                # --- Calculate Total Planned Workouts ---
                total_planned_workouts = 0
                if user_wp and user_wp.workout_plan and user_wp.start_date and user_wp.end_date and user_wp.start_date <= user_wp.end_date:
                    plan_duration_days = (user_wp.end_date - user_wp.start_date).days + 1
                    # Query the CLONED schedules linked to the user_wp
                    user_schedules = WorkoutSchedule.objects.filter(user_workout_plan=user_wp).order_by('week_number')
                    # Find the maximum week number defined in the user's schedules
                    max_schedule_week = user_schedules.aggregate(max_week=db_models.Max('week_number'))['max_week'] or 1

                    if user_schedules.exists():
                        schedule_dict = {s.week_number: s for s in user_schedules}
                        day_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

                        for day_num in range(1, plan_duration_days + 1):
                            # Calculate week number, looping back if plan duration > schedule duration
                            current_week_in_schedule = ((day_num - 1) // 7) % max_schedule_week + 1
                            day_index = (day_num - 1) % 7
                            current_schedule = schedule_dict.get(current_week_in_schedule)

                            if current_schedule and getattr(current_schedule, day_fields[day_index], None):
                                total_planned_workouts += 1
                        logger.info(f"Calculated total_planned_workouts: {total_planned_workouts} for UserWorkoutPlan {user_wp.id} over {plan_duration_days} days")
                    else:
                        logger.warning(f"No schedules found for UserWorkoutPlan {user_wp.id}, cannot calculate planned workouts.")
                elif user_wp:
                    logger.warning(f"Could not calculate total_planned_workouts for UserWorkoutPlan {user_wp.id} due to missing plan, dates, or invalid date range.")


            # --- Meal Plan Assignment & Cloning ---
            user_mp = None # Initialize
            if meal_plan:
                logger.info(f"Plan Assignment - Attempting to assign MealPlan ID {meal_plan.id} to UserProfile {updated_profile.id}")
                try:
                    # First, deactivate all existing meal plans for this user
                    # This ensures we don't have multiple active plans
                    UserMealPlan.objects.filter(user=updated_profile, is_active=True).update(is_active=False)
                    logger.info(f"Deactivated all existing meal plans for UserProfile {updated_profile.id}")

                    # Get the date for the start_date (use client's local date if provided)
                    local_date = request.data.get('local_date')
                    if local_date:
                        try:
                            # Parse the local_date string to a date object
                            today = datetime.strptime(local_date, '%Y-%m-%d').date()
                            logger.info(f"Using client's local date: {today} for meal plan start date")
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid local_date format: {local_date}. Using server date instead.")
                            today = timezone.now().date()
                    else:
                        today = timezone.now().date()
                        logger.info(f"No local_date provided. Using server date: {today} for meal plan start date")

                    # Create a new meal plan with the appropriate date as the start date
                    user_mp = UserMealPlan.objects.create(
                        user=updated_profile,
                        meal_plan=meal_plan,
                        start_date=today,
                        is_active=True,
                        daily_calories=getattr(meal_plan, 'daily_calories', 2000),
                        daily_protein=getattr(meal_plan, 'daily_protein', 150),
                        daily_carbs=getattr(meal_plan, 'daily_carbs', 200),
                        daily_fat=getattr(meal_plan, 'daily_fat', 70),
                        is_modified=False,
                        modifications={}
                    )
                    logger.info(f"Plan Assignment - Created new UserMealPlan (ID: {user_mp.id}, Start Date: {today}) for UserProfile {updated_profile.id}")

                    # Call deep cloning after creating the plan
                    deep_clone_meal_plan_for_user(user_mp)
                except Exception as mp_assign_error:
                    logger.error(f"Plan Assignment - FAILED to assign/clone MealPlan ID {meal_plan.id} to UserProfile {updated_profile.id}: {mp_assign_error}", exc_info=True)
                    user_mp = None

                # --- Calculate Total Planned Meals ---
                total_planned_meals = 0
                if user_mp and user_mp.meal_plan and user_mp.start_date and user_mp.end_date and user_mp.start_date <= user_mp.end_date:
                    plan_duration_days = (user_mp.end_date - user_mp.start_date).days + 1
                    # Query the CLONED schedules linked to the user_mp
                    user_schedules = MealSchedule.objects.filter(user_meal_plan=user_mp).order_by('week_number')
                    max_schedule_week = user_schedules.aggregate(max_week=db_models.Max('week_number'))['max_week'] or 1

                    if user_schedules.exists():
                        schedule_dict = {s.week_number: s for s in user_schedules}
                        day_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

                        for day_num in range(1, plan_duration_days + 1):
                            current_week_in_schedule = ((day_num - 1) // 7) % max_schedule_week + 1
                            day_index = (day_num - 1) % 7
                            current_schedule = schedule_dict.get(current_week_in_schedule)

                            if current_schedule:
                                daily_plan = getattr(current_schedule, day_fields[day_index], None)
                                if daily_plan:
                                    if daily_plan.breakfast: total_planned_meals += 1
                                    if daily_plan.lunch: total_planned_meals += 1
                                    if daily_plan.dinner: total_planned_meals += 1
                        logger.info(f"Calculated total_planned_meals: {total_planned_meals} for UserMealPlan {user_mp.id} over {plan_duration_days} days")
                    else:
                         logger.warning(f"No schedules found for UserMealPlan {user_mp.id}, cannot calculate planned meals.")
                elif user_mp:
                    logger.warning(f"Could not calculate total_planned_meals for UserMealPlan {user_mp.id} due to missing plan, dates, or invalid date range.")


            # --- Update UserProgress with Planned Totals ---
            progress, created_prog = UserProgress.objects.get_or_create(user=updated_profile)
            update_fields = []
            if workout_plan:
                progress.total_workouts_planned = total_planned_workouts
                update_fields.append('total_workouts_planned')
            if meal_plan:
                progress.total_meals_planned = total_planned_meals
                update_fields.append('total_meals_planned')

            if update_fields:
                progress.save(update_fields=update_fields)
                logger.info(f"Updated UserProgress {progress.id} with planned totals: {update_fields}")


            # --- Score Calculation ---
            if workout_plan or meal_plan:
                try:
                    score, score_created = UserScore.objects.get_or_create(user=updated_profile)
                    score.calculate_score()
                    logger.info(f"UserScore {'created' if score_created else 'updated'} for user {updated_profile.id}. New score: {score.total_score}")
                except TypeError as score_error:
                     logger.error(f"TypeError during score calculation for user {updated_profile.id}: {score_error}", exc_info=True)
                except Exception as score_error:
                     logger.error(f"Unexpected error during score calculation for user {updated_profile.id}: {score_error}", exc_info=True)

            response_data = {
                'success': True, 'message': 'Questionnaire submitted successfully',
                'profile': UserProfileSerializer(updated_profile).data,
                'workout_plan': WorkoutPlanSerializer(workout_plan).data if workout_plan else None,
                'meal_plan': MealPlanSerializer(meal_plan).data if meal_plan else None
            }
            return Response(response_data)
        except Exception as e:
            logger.error(f"Error during plan assignment/scoring for user {request.user.id}: {e}", exc_info=True)
            # Avoid returning generic 500 if profile was updated but plan assignment failed
            return Response({'success': False, 'message': f'Error assigning plans or calculating score: {str(e)}', 'profile_updated': True}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class HomeScreenView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        today = timezone.now().date()
        user = request.user
        profile = get_object_or_404(UserProfile, user=user)
        todays_log = WorkoutLog.objects.filter(user=profile, date__date=today).first()
        score = UserScore.objects.filter(user=profile).first()
        data = {
            'user': UserSerializer(user).data,
            'profile': UserProfileSerializer(profile).data,
            'todays_workout_log': WorkoutLogSerializer(todays_log).data if todays_log else None,
            'user_score': UserScoreSerializer(score).data if score else None,
        }
        return Response(data)

@method_decorator(ensure_csrf_cookie, name='dispatch')
class CSRFTokenView(APIView):
    permission_classes = [permissions.AllowAny]
    def get(self, request):
        return JsonResponse({'csrfToken': get_token(request)})

class LoginView(APIView):
    permission_classes = [permissions.AllowAny]
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')
        logger.info(f"LoginView: Received login attempt for email: {email}")
        if not email or not password:
            logger.warning("LoginView: Email or password missing. Returning 400.")
            return Response({'non_field_errors': ['Email and password required.']}, status=status.HTTP_400_BAD_REQUEST)
        
        user = authenticate(request, username=email, password=password)
        
        if user is None:
            logger.warning(f"LoginView: Authentication failed for email: {email}. Returning 400.")
            return Response({'non_field_errors': ['Invalid credentials.']}, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"LoginView: Authentication successful for user: {user.email}. Generating tokens.")
        refresh = RefreshToken.for_user(user)
        
        profile = get_object_or_404(UserProfile, user=user)
        questionnaire_completed = bool(profile.gender and profile.age and profile.height and profile.weight)
        active_workout_plan = UserWorkoutPlan.objects.filter(user=profile, is_active=True).exists()
        active_meal_plan = UserMealPlan.objects.filter(user=profile, is_active=True).exists()
        
        logger.info(f"LoginView: Login successful for user {user.email}. Returning 200.")
        return Response({'refresh': str(refresh), 'access': str(refresh.access_token), 'user': UserSerializer(user).data, 'questionnaire_completed': questionnaire_completed, 'has_active_workout_plan': active_workout_plan, 'has_active_meal_plan': active_meal_plan})

class LogoutView(APIView):
     permission_classes = (IsAuthenticated,)
     def post(self, request):
          try:
               refresh_token = request.data["refresh"]
               token = RefreshToken(refresh_token)
               token.blacklist()
               return Response(status=status.HTTP_205_RESET_CONTENT)
          except Exception as e:
               logger.error(f"Error during logout: {e}", exc_info=True)
               return Response({"detail": "Error during logout."}, status=status.HTTP_400_BAD_REQUEST)

class TokenView(APIView):
     permission_classes = [permissions.AllowAny]
     def post(self, request):
          return Response({"detail": "Endpoint not implemented."}, status=status.HTTP_501_NOT_IMPLEMENTED)

class PasswordResetView(APIView):
    permission_classes = [AllowAny]
    def post(self, request):
        form = CustomPasswordResetForm(request.data)
        if form.is_valid():
            email = form.cleaned_data['email']
            try: user = get_user_model().objects.get(email=email)
            except get_user_model().DoesNotExist: return Response({'detail': 'Password reset e-mail has been sent.'}, status=status.HTTP_200_OK)
            if settings.DEBUG: return Response({'detail': 'Password reset email would be sent in production.', 'development_mode': True, 'email': email})
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)
            reset_url = f"{settings.FRONTEND_URL}/reset-password/{uid}/{token}/"
            try:
                context = {'user': user, 'reset_url': reset_url, 'site_name': 'AC-FIT', 'site_domain': request.get_host()}
                subject = "Password Reset for AC-FIT"
                email_html_message = render_to_string('account/email/password_reset_key_message.html', context)
                email_plaintext_message = render_to_string('account/email/password_reset_key_message.txt', context)
                send_mail(subject, email_plaintext_message, settings.DEFAULT_FROM_EMAIL, [email], html_message=email_html_message, fail_silently=False)
                logger.info(f"Password reset email sent to {email}")
                return Response({'detail': 'Password reset e-mail has been sent.'})
            except Exception as mail_error: logger.error(f"Failed to send password reset email to {email}: {mail_error}", exc_info=True); return Response({'error': 'Failed to send reset email.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(form.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetConfirmView(APIView):
    permission_classes = [AllowAny]
    def post(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = get_user_model().objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, get_user_model().DoesNotExist): user = None
        if user is not None and default_token_generator.check_token(user, token):
            form = CustomSetPasswordForm(user=user, data=request.data)
            if form.is_valid(): form.save(); return Response({'detail': 'Password has been reset.'})
            return Response(form.errors, status=status.HTTP_400_BAD_REQUEST)
        return Response({'detail': 'Password reset link is invalid or has expired.'}, status=status.HTTP_400_BAD_REQUEST)

# User Score Views
class UserScoreView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, format=None):
        logger.debug(f"UserScoreView get() called for user: {request.user.email}")
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            score, created = UserScore.objects.get_or_create(user=user_profile)
            if created: logger.info(f"UserScore created for profile: {user_profile.id} in UserScoreView")
            serializer = UserScoreSerializer(score)
            return Response(serializer.data)
        except Http404: return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e: logger.error(f"Unexpected error in UserScoreView: {str(e)}", exc_info=True); return Response({"detail": "Error retrieving user score."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserScoreBreakdownView(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DetailedScoreSerializer
    def get_object(self):
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        score, created = UserScore.objects.get_or_create(user=user_profile)
        needs_recalculation = created or not score.last_calculated or (timezone.now() - score.last_calculated > timedelta(hours=1))
        if needs_recalculation: logger.info(f"Recalculating score for user {self.request.user.id}"); score.calculate_score()
        return score

class UserScoreHistoryView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        timeframe = request.query_params.get('timeframe', 'week').lower()
        end_date = timezone.now().date()
        if timeframe == 'month': start_date = end_date - timedelta(days=30)
        elif timeframe == 'year': start_date = end_date - timedelta(days=365)
        else: start_date = end_date - timedelta(days=7)
        daily_scores = DailyScore.objects.filter(user=user_profile, date__range=[start_date, end_date]).order_by('date')
        history = [{'date': ds.date.strftime('%Y-%m-%d'), 'score': ds.total_score} for ds in daily_scores]
        if not history:
             current_score_obj = UserScore.objects.filter(user=user_profile).first()
             if current_score_obj: history.append({'date': end_date.strftime('%Y-%m-%d'), 'score': current_score_obj.total_score})
        return Response({'timeframe': timeframe, 'start_date': start_date.strftime('%Y-%m-%d'), 'end_date': end_date.strftime('%Y-%m-%d'), 'score_history': history})

class UserScorePercentileView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        user_score_obj = get_object_or_404(UserScore, user=user_profile)
        user_score = user_score_obj.total_score or 0
        all_scores = UserScore.objects.filter(total_score__isnull=False).values_list('total_score', flat=True)
        total_users = len(all_scores)
        if total_users == 0: percentile, rank, average_score = 100.0, 1, 0
        else:
            lower_scores_count = sum(1 for score in all_scores if score < user_score)
            percentile = (lower_scores_count / total_users) * 100
            rank = UserScore.objects.filter(total_score__gt=user_score).count() + 1
            average_score = sum(all_scores) / total_users
        return Response({'user_score': user_score, 'percentile': round(percentile, 1), 'rank': rank, 'total_users': total_users, 'average_score': round(average_score, 1)})

# User Progress Views
class UserProgressView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, format=None):
        logger.debug(f"UserProgressView get() called for user: {request.user.email}")
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            progress, created = UserProgress.objects.get_or_create(user=user_profile)
            if created: logger.info(f"UserProgress created for profile: {user_profile.id} in UserProgressView")
            serializer = UserProgressSerializer(progress)
            return Response(serializer.data)
        except Http404: return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e: logger.error(f"Unexpected error in UserProgressView get: {str(e)}", exc_info=True); return Response({"detail": "Error retrieving user progress."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    def post(self, request, *args, **kwargs):
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            progress, _ = UserProgress.objects.get_or_create(user=user_profile)
            serializer = UserProgressSerializer(progress, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            score, _ = UserScore.objects.get_or_create(user=user_profile)
            score.calculate_score()
            return Response(serializer.data)
        except Http404: return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except serializers.ValidationError as e: logger.warning(f"Validation error updating UserProgress for user {request.user.id}: {e.detail}"); return Response({"detail": "Invalid data provided.", "errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e: logger.error(f"Unexpected error in UserProgressAPIView post: {str(e)}", exc_info=True); return Response({"detail": "Error updating user progress."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Social authentication views
class GoogleLoginView(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    client_class = OAuth2Client
    def process_login(self): super().process_login() # Signal handles profile creation

class FacebookLoginView(SocialLoginView):
    adapter_class = FacebookOAuth2Adapter
    client_class = OAuth2Client
    def process_login(self): super().process_login() # Signal handles profile creation

class AppleLoginView(SocialLoginView):
    adapter_class = AppleOAuth2Adapter
    client_class = OAuth2Client
    def process_login(self): super().process_login() # Signal handles profile creation

# Test email view
class TestEmailView(APIView):
    permission_classes = [permissions.IsAdminUser]
    def post(self, request):
        if not settings.DEBUG: return Response({"error": "Debug only."}, status=status.HTTP_403_FORBIDDEN)
        email_type = request.data.get('type', 'confirmation')
        to_email = request.data.get('email')
        if not to_email: return Response({"error": "Email required."}, status=status.HTTP_400_BAD_REQUEST)
        if email_type == 'confirmation':
            from allauth.account.models import EmailAddress, EmailConfirmationHMAC
            email_address, _ = EmailAddress.objects.get_or_create(user=request.user, email=to_email)
            EmailConfirmationHMAC(email_address).send()
            return Response({"message": "Confirmation email sent"})
        elif email_type == 'password_reset':
            from django.contrib.auth.forms import PasswordResetForm
            form = PasswordResetForm({'email': to_email})
            if form.is_valid():
                form.save(request=request, use_https=request.is_secure(), email_template_name='account/email/password_reset_key_message.txt', html_email_template_name='account/email/password_reset_key_message.html')
                return Response({"message": "Password reset email sent"})
            return Response(form.errors, status=status.HTTP_400_BAD_REQUEST)
        return Response({"error": "Invalid email type."}, status=status.HTTP_400_BAD_REQUEST)

# Auth Debug View
class AuthDebugView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        logger.info(f"Auth header: {auth_header}")
        return Response({'authenticated': request.user.is_authenticated, 'user_id': request.user.id, 'email': request.user.email, 'auth_method': self._get_auth_method(request), 'request_headers': {k: v for k, v in request.META.items() if k.startswith('HTTP_')}, 'timestamp': timezone.now().isoformat()})
    def _get_auth_method(self, request):
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '): return 'JWT'
        elif auth_header.startswith('Token '): return 'Token Authentication'
        return 'Unknown'

# API Views for specific functionalities (moved from bottom)
class UserScoreAPIView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, format=None):
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            score, created = UserScore.objects.get_or_create(user=user_profile)
            if created:
                logger.info(f"UserScore created for profile: {user_profile.id}")
            serializer = UserScoreSerializer(score)
            return Response(serializer.data)
        except Http404:
            return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving user score for user {request.user.id}: {str(e)}", exc_info=True)
            return Response({"detail": "Error retrieving user score."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserProgressAPIView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, format=None):
        logger.debug(f"UserProgressAPIView get() called for user: {request.user.email}")
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            progress, created = UserProgress.objects.get_or_create(user=user_profile)
            if created: logger.info(f"UserProgress created for profile: {user_profile.id} in UserProgressAPIView")
            serializer = UserProgressSerializer(progress)
            return Response(serializer.data)
        except Http404: return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e: logger.error(f"Unexpected error in UserProgressAPIView get: {str(e)}", exc_info=True); return Response({"detail": "Error retrieving user progress."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    def post(self, request, *args, **kwargs):
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            progress, _ = UserProgress.objects.get_or_create(user=user_profile)
            serializer = UserProgressSerializer(progress, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            score, _ = UserScore.objects.get_or_create(user=user_profile)
            score.calculate_score()
            return Response(serializer.data)
        except Http404: return Response({"detail": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)
        except serializers.ValidationError as e: logger.warning(f"Validation error updating UserProgress for user {request.user.id}: {e.detail}"); return Response({"detail": "Invalid data provided.", "errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e: logger.error(f"Unexpected error in UserProgressAPIView post: {str(e)}", exc_info=True); return Response({"detail": "Error updating user progress."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_calories(request):
    try:
        user_profile = get_object_or_404(UserProfile, user=request.user)
        # --- Get user's local date ---
        try:
            user_tz = pytz.timezone(user_profile.timezone)
        except pytz.UnknownTimeZoneError:
            logger.warning(f"Invalid timezone '{user_profile.timezone}' for user {request.user.id}. Using UTC.")
            user_tz = pytz.utc
        today_local = timezone.now().astimezone(user_tz).date()
        # --- End get user's local date ---

        # --- Use today_local for filtering ---
        # Calculate calories consumed today from completed MealLogs
        meal_calories = MealLog.objects.filter(
            user=user_profile,
            date=today_local, # Use local date
            is_completed=True
        ).aggregate(total=Sum('meal__calories'))['total'] or 0

        # Calculate calories burned today from completed WorkoutLogs
        workout_calories = WorkoutLog.objects.filter(
            user=user_profile,
            date=today_local, # Use local date
            is_completed=True
        ).aggregate(total=Sum('calories_burned'))['total'] or 0

        # Get daily target calories
        user_meal_plan = UserMealPlan.objects.filter(user=user_profile, is_active=True).order_by('-start_date').first()
        daily_target = user_meal_plan.daily_calories or 2000 if user_meal_plan else 2000 # Default to 2000 if no plan
        # --- End use today_local for filtering ---

        return Response({"status": "success", "data": {"consumed": meal_calories, "burned": workout_calories, "target": daily_target, "net": meal_calories - workout_calories, "remaining": daily_target - meal_calories + workout_calories}})
    except Exception as e:
        logger.error(f"Error getting user calories for user {request.user.id}: {str(e)}", exc_info=True)
        return Response({"error": "Error retrieving calories data", "status": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_user_weight(request):
    try:
        user_profile = get_object_or_404(UserProfile, user=request.user)
        new_weight = request.data.get('weight')
        if new_weight is None: return Response({'message': 'Weight is required', 'status': 'error'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            new_weight = float(new_weight)
            if new_weight <= 0: return Response({'message': 'Weight must be a positive number', 'status': 'error'}, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError): return Response({'message': 'Weight must be a valid number', 'status': 'error'}, status=status.HTTP_400_BAD_REQUEST)
        if not user_profile.initial_weight: user_profile.initial_weight = user_profile.weight if user_profile.weight else new_weight
        previous_weight = user_profile.weight
        user_profile.weight = new_weight
        user_profile.save()
        score_updated = False
        if previous_weight is None or abs(float(previous_weight) - new_weight) >= 0.5:
            try: score, _ = UserScore.objects.get_or_create(user=user_profile); score.calculate_score(); score_updated = True
            except Exception as score_e: logger.error(f"Error recalculating score after weight update for user {user_profile.id}: {score_e}", exc_info=True)
        return Response({'message': 'Weight updated successfully', 'status': 'success', 'data': {'weight': user_profile.weight, 'initial_weight': user_profile.initial_weight, 'score_updated': score_updated}})
    except Exception as e: logger.error(f"Error updating weight for user {request.user.id}: {str(e)}", exc_info=True); return Response({'message': f'Error updating weight: {str(e)}', 'status': 'error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserProfileAPIView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            serializer = UserProfileSerializer(user_profile)
            return Response({"message": "Profile retrieved successfully", "status": "success", "data": serializer.data})
        except Exception as e: logger.error(f"Error retrieving profile for user {request.user.id}: {e}", exc_info=True); return Response({"message": f"Error retrieving profile: {str(e)}", "status": "error"}, status=500)
    def patch(self, request):
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            serializer = UserProfileSerializer(user_profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                score, _ = UserScore.objects.get_or_create(user=user_profile)
                score.calculate_score()
                logger.info(f"Profile updated and score recalculated for user {request.user.id}")
                return Response({"message": "Profile updated successfully", "status": "success", "data": serializer.data})
            logger.warning(f"Invalid profile update data for user {request.user.id}: {serializer.errors}")
            return Response({"message": "Invalid data provided", "status": "error", "errors": serializer.errors}, status=400)
        except Exception as e: logger.error(f"Error updating profile for user {request.user.id}: {e}", exc_info=True); return Response({"message": f"Error updating profile: {str(e)}", "status": "error"}, status=500)

class UserCaloriesView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        try:
            user_profile = get_object_or_404(UserProfile, user=request.user)
            # --- Get user's local date ---
            try:
                user_tz = pytz.timezone(user_profile.timezone)
            except pytz.UnknownTimeZoneError:
                logger.warning(f"Invalid timezone '{user_profile.timezone}' for user {request.user.id}. Using UTC.")
                user_tz = pytz.utc
            today_local = timezone.now().astimezone(user_tz).date()
            # --- End get user's local date ---

            # --- Use today_local for filtering ---
            # Assuming WorkoutLog.date is DateField, removed unnecessary __date lookup
            workout_calories = WorkoutLog.objects.filter(
                user=user_profile,
                date=today_local, # Use local date
                is_completed=True
            ).aggregate(total=Sum('calories_burned'))['total'] or 0
            # Assuming MealLog.date is DateField, removed unnecessary __date lookup
            # Use meal__calories to get calories from the associated meal
            meal_calories = MealLog.objects.filter(
                user=user_profile,
                date=today_local, # Use local date
                is_completed=True
            ).aggregate(total=Sum('meal__calories'))['total'] or 0
            # --- End use today_local for filtering ---

            user_meal_plan = UserMealPlan.objects.filter(user=user_profile, is_active=True).order_by('-start_date').first()
            daily_target = user_meal_plan.daily_calories or 2000 if user_meal_plan else 2000
            # ADDED: Return response in the try block
            return Response({"status": "success", "data": {"consumed": meal_calories, "burned": workout_calories, "target": daily_target, "net": meal_calories - workout_calories, "remaining": daily_target - meal_calories + workout_calories}})
        except Exception as e:
            logger.error(f"Error retrieving calories data for user {request.user.id}: {str(e)}", exc_info=True)
            # MODIFIED: Use consistent error response format
            return Response({"error": f"Error retrieving calories data: {str(e)}", "status": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
