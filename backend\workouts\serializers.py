from rest_framework import serializers
from .models import (
    WorkoutPlan, WorkoutDay, WorkoutSession, WorkoutSection, Exercise,
    ExerciseInstance, WorkoutLog, WorkoutSessionLog, ExerciseLog, WorkoutSchedule,
    WorkoutReminder, Equipment, UserWorkoutPlan # Removed UserEquipment
)
# Import UserProfile for score/progress access if needed
from accounts.models import UserProfile, UserScore, UserProgress

# Serializer for Exercise model (moved from accounts)
class ExerciseSerializer(serializers.ModelSerializer):
    cover_image_url = serializers.SerializerMethodField()
    media_file_url = serializers.SerializerMethodField()
    # Add backward compatibility fields
    video_url = serializers.SerializerMethodField()
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = Exercise
        fields = '__all__' # Add 'cover_image_url', 'media_file_url', 'video_url', 'image_url'

    def get_cover_image_url(self, obj):
        request = self.context.get('request')
        if obj.cover_image and hasattr(obj.cover_image, 'url'):
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None

    def get_media_file_url(self, obj):
        request = self.context.get('request')
        if obj.media_file and hasattr(obj.media_file, 'url'):
            if request:
                return request.build_absolute_uri(obj.media_file.url)
            return obj.media_file.url
        return None

    def get_video_url(self, obj):
        # For backward compatibility, return media_file_url as video_url
        return self.get_media_file_url(obj)

    def get_image_url(self, obj):
        # For backward compatibility, return cover_image_url as image_url
        return self.get_cover_image_url(obj)

# Serializer for ExerciseInstance model (moved from accounts)
class ExerciseInstanceSerializer(serializers.ModelSerializer):
    exercise_details = ExerciseSerializer(source='exercise', read_only=True)

    class Meta:
        model = ExerciseInstance
        fields = '__all__'

# Serializer for WorkoutSection model (moved from accounts)
class WorkoutSectionSerializer(serializers.ModelSerializer):
    # Nested ExerciseInstanceSerializer for detail views
    exercises = ExerciseInstanceSerializer(many=True, read_only=True) # Uncommented

    class Meta:
        model = WorkoutSection
        fields = '__all__' # 'exercises' is implicitly included by '__all__' when defined on serializer

# Serializer for WorkoutSession model
class WorkoutSessionSerializer(serializers.ModelSerializer):
    # Nested WorkoutSectionSerializer for detail views
    sections = WorkoutSectionSerializer(many=True, read_only=True)

    class Meta:
        model = WorkoutSession
        fields = '__all__' # 'sections' is implicitly included by '__all__' when defined on serializer

    def to_representation(self, instance):
        # Get the default representation
        representation = super().to_representation(instance)

        # Ensure id field is the actual WorkoutSession ID
        representation['id'] = instance.id

        # Add a debug log to verify the correct ID is being used
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"WorkoutSession {instance.id} has id={instance.id}")

        return representation

# Serializer for WorkoutDay model (moved from accounts)
class WorkoutDaySerializer(serializers.ModelSerializer):
    # Nested WorkoutSessionSerializer for detail views
    sessions = WorkoutSessionSerializer(many=True, read_only=True)
    cover_image_url = serializers.SerializerMethodField()

    class Meta:
        model = WorkoutDay
        fields = '__all__' # 'sessions' is implicitly included by '__all__' when defined on serializer

    def get_cover_image_url(self, obj):
        request = self.context.get('request')
        if obj.cover_image and hasattr(obj.cover_image, 'url'):
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None

    def to_representation(self, instance):
        # Get the default representation
        representation = super().to_representation(instance)

        # Ensure id field is the actual WorkoutDay ID
        representation['id'] = instance.id

        # Add a debug log to verify the correct ID is being used
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"WorkoutDay {instance.id} has id={instance.id}")

        return representation

# Serializer for WorkoutPlan model (moved from accounts)
class WorkoutPlanSerializer(serializers.ModelSerializer):
    # Consider adding nested WorkoutDaySerializer if needed for detail views
    # workout_days = WorkoutDaySerializer(many=True, read_only=True) # Example
    cover_image_url = serializers.SerializerMethodField()

    class Meta:
        model = WorkoutPlan
        fields = '__all__' # Add 'workout_days', 'cover_image_url' to fields if using nested serializer

    def get_cover_image_url(self, obj):
        request = self.context.get('request')
        if obj.cover_image and hasattr(obj.cover_image, 'url'):
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None

# --- NEW: Serializer for ExerciseLog pointing to WorkoutSessionLog ---
class ExerciseLogSerializer(serializers.ModelSerializer):
    # exercise_details = ExerciseSerializer(source='exercise', read_only=True) # Optional: include full exercise details

    class Meta:
        model = ExerciseLog
        fields = '__all__'
        # workout_session_log should be writable for creating/updating logs
        # Make sure workout_session_log is provided in the view when creating/updating
        read_only_fields = ('created_at', 'updated_at')

# --- NEW: Serializer for WorkoutSessionLog ---
class WorkoutSessionLogSerializer(serializers.ModelSerializer):
    # Nested ExerciseLogSerializer for detail views
    exercise_logs = ExerciseLogSerializer(many=True, read_only=True)
    workout_session_details = WorkoutSessionSerializer(source='workout_session', read_only=True)
    # User is available via workout_log

    class Meta:
        model = WorkoutSessionLog
        fields = [
            'id', 'workout_log', 'workout_session', 'workout_session_details',
            'date', 'is_completed', 'completion_time', 'duration_minutes',
            'calories_burned', 'feeling', 'intensity', 'notes', 'created_at',
            'updated_at', 'exercise_logs'
        ]
        read_only_fields = ('workout_log', 'date', 'created_at', 'updated_at', 'exercise_logs', 'workout_session_details')
        # Make workout_session writable for creation (if needed, view might handle it)
        # extra_kwargs = {
        #     'workout_session': {'write_only': True, 'required': True}
        # }

    def to_representation(self, instance):
        # Get the default representation
        representation = super().to_representation(instance)

        # Ensure workout_session field is the actual WorkoutSession ID
        if instance.workout_session:
            representation['workout_session'] = instance.workout_session.id

            # Add a debug log to verify the correct ID is being used
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"WorkoutSessionLog {instance.id} has workout_session_id={instance.workout_session.id}")

        return representation

# --- UPDATED: Serializer for WorkoutLog (now represents the whole day) ---
class WorkoutLogSerializer(serializers.ModelSerializer):
    workout_day_details = WorkoutDaySerializer(source='workout_day', read_only=True)
    # Nested WorkoutSessionLogSerializer for detail views
    session_logs = WorkoutSessionLogSerializer(many=True, read_only=True)

    # Make workout_day writable for creation/update but also readable for the API
    workout_day = serializers.PrimaryKeyRelatedField(
        queryset=WorkoutDay.objects.all(),
        required=False  # Not required for updates
    )

    class Meta:
        model = WorkoutLog
        fields = [
            'id', 'user', 'workout_day', 'workout_day_details', 'date',
            'is_completed', 'completion_time', 'duration_minutes', 'calories_burned',
            'feeling', 'intensity', 'notes', 'created_at', 'updated_at',
            'session_logs' # Add nested session logs
        ]
        read_only_fields = ('user', 'completion_time', 'created_at', 'updated_at', 'workout_day_details', 'session_logs')

    def to_representation(self, instance):
        # Get the default representation
        representation = super().to_representation(instance)

        # Ensure workout_day field is the actual WorkoutDay ID
        if instance.workout_day:
            representation['workout_day'] = instance.workout_day.id

            # Add a debug log to verify the correct ID is being used
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"WorkoutLog {instance.id} has workout_day_id={instance.workout_day.id}")

        return representation

# Serializer for WorkoutSchedule model (needed by workouts/views/schedule.py)
class WorkoutScheduleSerializer(serializers.ModelSerializer):
    # Explicitly include the days field
    days = serializers.JSONField(required=False)

    class Meta:
        # from .models import WorkoutSchedule # No longer need local import as it's imported above
        model = WorkoutSchedule
        fields = '__all__'

# Serializer for WorkoutReminder model (needed by workouts/views/schedule.py)
class WorkoutReminderSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkoutReminder
        fields = '__all__'

# Removed UserEquipmentSerializer
# class UserEquipmentSerializer(serializers.ModelSerializer):
#     ... (removed) ...

# Serializer for Equipment model (needed by workouts/views/schedule.py)
class EquipmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Equipment
        fields = '__all__'

# --- ADD UserWorkoutPlanSerializer ---
class UserWorkoutPlanSerializer(serializers.ModelSerializer):
    # Optionally include nested serializers for related fields if needed
    # workout_plan_details = WorkoutPlanSerializer(source='workout_plan', read_only=True)
    # user_details = UserProfileSerializer(source='user', read_only=True) # Need to import UserProfileSerializer

    class Meta:
        model = UserWorkoutPlan
        fields = '__all__'
        read_only_fields = ('user', 'created_at', 'updated_at', 'version', 'parent_plan', 'is_modified', 'modifications') # User is set implicitly or via signals

# Add other workout-related serializers here as needed
