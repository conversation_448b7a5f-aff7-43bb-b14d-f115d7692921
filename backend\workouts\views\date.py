from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
import pytz
from django.utils import timezone
import logging

from accounts.models import UserProfile
from workouts.models import UserWorkoutPlan, WorkoutDay, WorkoutSession, WorkoutSection, WorkoutLog, WorkoutSessionLog, ExerciseLog

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_workout_for_date(request):
    """Get workout details for a specific calendar date."""
    try:
        user_profile = get_object_or_404(UserProfile, user=request.user)

        # Get the date parameter from the request
        date_str = request.query_params.get('date')
        if not date_str:
            return Response({"detail": "Date parameter is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Parse the date string
            target_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            return Response({"detail": "Invalid date format. Use YYYY-MM-DD."}, status=status.HTTP_400_BAD_REQUEST)

        # Get user's active workout plan that covers the target date
        try:
            user_workout_plan = UserWorkoutPlan.objects.filter(
                user=user_profile,
                start_date__lte=target_date, # Plan must have started by the target date
                end_date__gte=target_date,   # Plan must not have ended before the target date
                is_active=True
            ).latest('created_at') # Get the latest active one covering the date
        except UserWorkoutPlan.DoesNotExist:
            # No active plan found covering the target date
            logger.info(f"No active workout plan found for user {user_profile.id} covering date {target_date}")
            return Response({
                "date": target_date.strftime("%Y-%m-%d"),
                "workout_day": None,
                "workout_sessions": [],
                "workout_logs": [],
                "is_rest_day": False, # Don't show as rest day for new accounts
                "no_plan_assigned": True, # Add flag to indicate no plan is assigned
                "message": f"No active workout plan found for date {target_date.strftime('%Y-%m-%d')}."
            }, status=status.HTTP_200_OK) # Return 200 with empty data

        # Calculate the program day for this date
        program_day = (target_date - user_workout_plan.start_date).days + 1

        # Check if the program has looped
        is_looped = False
        if user_workout_plan.has_completed_one_cycle:
            # If the plan has completed one cycle, we need to adjust the program day
            total_days = user_workout_plan.get_total_days()
            if program_day > total_days:
                # Calculate the adjusted program day after looping
                program_day = ((program_day - 1) % total_days) + 1
                is_looped = True
                logger.info(f"Program has looped. Original day: {(target_date - user_workout_plan.start_date).days + 1}, Adjusted day: {program_day}")

        # Find the workout day for this program day
        workout_day = None

        # Try to find the workout day with matching day_number in the user's plan
        workout_day = WorkoutDay.objects.filter(
            user_workout_plan=user_workout_plan,
            day_number=program_day
        ).first()

        if workout_day:
            logger.info(f"Found workout day with day_number={program_day}: {workout_day.name}")
        else:
            # If not found by day_number, try to find by week and day in week
            from workouts.models import WorkoutSchedule

            # Calculate week number and day in week
            week_number = (program_day - 1) // 7 + 1
            day_in_week = (program_day - 1) % 7

            schedule = WorkoutSchedule.objects.filter(
                user_workout_plan=user_workout_plan,
                week_number=week_number
            ).first()

            if schedule:
                # Get the workout day ID for this day of the week
                day_names = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                day_field_name = day_names[day_in_week]
                workout_day_id = getattr(schedule, day_field_name)

                if workout_day_id:
                    workout_day = WorkoutDay.objects.filter(id=workout_day_id).first()
                    if workout_day:
                        logger.info(f"Found workout day from schedule: {workout_day.name}")

        # If no workout day found, return a rest day response
        if not workout_day:
            logger.info(f"No workout day found for program day {program_day}")
            return Response({
                "date": target_date.strftime("%Y-%m-%d"),
                "program_day": program_day,
                "workout_day": None,
                "workout_sessions": [],
                "workout_logs": [],
                "is_rest_day": True,
                "is_looped": is_looped,
                "has_completed_one_cycle": user_workout_plan.has_completed_one_cycle,
                "message": f"Rest day for date {target_date.strftime('%Y-%m-%d')} (program day {program_day})."
            }, status=status.HTTP_200_OK)

        # Get workout sessions for this day - ensure we only get user-specific sessions
        workout_sessions = WorkoutSession.objects.filter(
            workout_day=workout_day,
            user_workout_plan=user_workout_plan  # This ensures we only get sessions for this specific user
        ).order_by('order')

        # Log the session IDs for debugging
        logger.info(f"Found {workout_sessions.count()} user-specific workout sessions for user {user_profile.id}, day {workout_day.id}")
        for session in workout_sessions:
            logger.info(f"Session ID: {session.id}, Name: {session.name}, User Plan: {session.user_workout_plan_id}")

        # Serialize workout sessions
        workout_sessions_data = []
        for session in workout_sessions:
            # Get workout sections for this session - ensure we only get user-specific sections
            sections = WorkoutSection.objects.filter(
                workout_session=session,
                user_workout_plan=user_workout_plan  # This ensures we only get sections for this specific user
            ).order_by('order')

            logger.info(f"Found {sections.count()} sections for session {session.id} and user workout plan {user_workout_plan.id}")

            # Serialize sections
            sections_data = []
            for section in sections:
                # Get exercises for this section - ensure we only get user-specific exercises
                exercises = section.exercises.filter(
                    user_workout_plan=user_workout_plan  # This ensures we only get exercises for this specific user
                ).order_by('order')

                logger.info(f"Found {exercises.count()} exercises for section {section.id} and user workout plan {user_workout_plan.id}")

                # Serialize exercises
                exercises_data = []
                for exercise in exercises:
                    exercise_data = {
                        'id': exercise.id,
                        'exercise_details': {
                            'id': exercise.exercise.id,
                            'name': exercise.exercise.name,
                            'description': exercise.exercise.description,
                            'muscle_group': exercise.exercise.muscle_group,
                            'equipment_required': exercise.exercise.equipment_required,
                            'difficulty_level': getattr(exercise.exercise, 'difficulty_level', None),
                            'exercise_type': getattr(exercise.exercise, 'exercise_type', None),
                            'media_file_url': request.build_absolute_uri(exercise.exercise.media_file.url) if exercise.exercise.media_file else None,
                            'video_url': request.build_absolute_uri(exercise.exercise.media_file.url) if exercise.exercise.media_file else None,  # Use media_file as video_url for backward compatibility
                            'image_url': request.build_absolute_uri(exercise.exercise.cover_image.url) if exercise.exercise.cover_image else None,  # Use cover_image as image_url for backward compatibility
                        },
                        'sets': getattr(exercise, 'sets', 0),
                        'reps': getattr(exercise, 'reps', 0),
                        'duration': getattr(exercise, 'duration_seconds', None) or getattr(exercise, 'duration', None),
                        'rest_time': getattr(exercise, 'rest_seconds', None) or getattr(exercise, 'rest_time', None),
                        'order': getattr(exercise, 'order', 0),
                        'weight': getattr(exercise, 'weight', 0),
                        'notes': getattr(exercise, 'notes', ''),
                    }
                    exercises_data.append(exercise_data)

                section_data = {
                    'id': section.id,
                    'name': getattr(section, 'name', ''),
                    'description': getattr(section, 'description', '') or getattr(section, 'notes', ''),
                    'order': getattr(section, 'order', 0),
                    'exercises': exercises_data,
                }
                sections_data.append(section_data)

            session_data = {
                'id': session.id,
                'name': getattr(session, 'name', ''),
                'description': getattr(session, 'description', ''),
                'order': getattr(session, 'order', 0),
                'duration': getattr(session, 'duration', 0),
                'scheduled_time': getattr(session, 'scheduled_time', None),
                'calories_burn_estimate': getattr(session, 'calories_burn_estimate', 0),
                'sections': sections_data,
            }
            workout_sessions_data.append(session_data)

        # Get or create workout logs for this day
        workout_logs = WorkoutLog.objects.filter(
            user=user_profile,
            workout_day=workout_day,
            date=target_date
        )

        # If no logs exist and this is today or a future date, create them
        today = timezone.now().date()
        is_past_date = target_date < today
        is_future_date = target_date > today

        if not workout_logs.exists() and not is_past_date:
            # Create a new workout log
            workout_log = WorkoutLog.objects.create(
                user=user_profile,
                workout_day=workout_day,
                date=target_date,
                is_completed=False
            )

            # Create session logs for each session
            for session in workout_sessions:
                session_log = WorkoutSessionLog.objects.create(
                    workout_log=workout_log,
                    workout_session=session,
                    is_completed=False
                )

                # Create exercise logs for each exercise in each section - ensure we only get user-specific sections and exercises
                for section in WorkoutSection.objects.filter(
                    workout_session=session,
                    user_workout_plan=user_workout_plan  # This ensures we only get sections for this specific user
                ):
                    logger.info(f"Creating exercise logs for section {section.id} in session {session.id}")
                    for exercise in section.exercises.filter(
                        user_workout_plan=user_workout_plan  # This ensures we only get exercises for this specific user
                    ):
                        logger.info(f"Creating exercise log for exercise {exercise.id} in section {section.id}")
                        # ExerciseLog expects an Exercise object, not an ExerciseInstance
                        # Get the base Exercise object from the ExerciseInstance
                        base_exercise = exercise.exercise if hasattr(exercise, 'exercise') else None

                        if base_exercise:
                            ExerciseLog.objects.create(
                                workout_session_log=session_log,
                                exercise=base_exercise,  # Use the base Exercise object
                                actual_sets=0,
                                actual_reps=0,
                                actual_weight=getattr(exercise, 'weight', 0),
                                is_completed=False
                            )

            # Refresh the queryset
            workout_logs = WorkoutLog.objects.filter(
                user=user_profile,
                workout_day=workout_day,
                date=target_date
            )

        # Serialize workout logs
        from workouts.serializers import WorkoutLogSerializer
        workout_logs_data = WorkoutLogSerializer(workout_logs, many=True, context={'request': request}).data

        # Prepare the response
        workout_day_data = {
            'id': workout_day.id,
            'name': getattr(workout_day, 'name', ''),
            'description': getattr(workout_day, 'description', ''),
            'day_number': getattr(workout_day, 'day_number', 0),
            'total_duration': getattr(workout_day, 'total_duration', 0),
            'calories_burn_estimate': getattr(workout_day, 'calories_burn_estimate', 0),
            'cover_image_url': request.build_absolute_uri(workout_day.cover_image.url) if hasattr(workout_day, 'cover_image') and workout_day.cover_image else None,
        }

        # Construct the response
        response_data = {
            "date": target_date.strftime("%Y-%m-%d"),
            "program_day": program_day,
            "workout_day": workout_day_data,
            "workout_sessions": workout_sessions_data,
            "workout_logs": workout_logs_data,
            "is_rest_day": False,
            "is_looped": is_looped,
            "has_completed_one_cycle": user_workout_plan.has_completed_one_cycle,
            "is_past_date": is_past_date,
            "is_future_date": is_future_date,
            "message": f"Workout details for date {target_date.strftime('%Y-%m-%d')} (program day {program_day})."
        }

        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        # Log the full exception for debugging
        logger.exception(f"Error in get_workout_for_date for user {request.user.id}, date {date_str}: {e}")
        return Response({
            "message": f"Error retrieving workout for date {date_str}: {str(e)}",
            "status": "error"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
