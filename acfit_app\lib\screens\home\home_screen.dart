import 'package:flutter/material.dart';
import 'painters.dart';
import 'package:provider/provider.dart'; // Import Provider
import 'package:dio/dio.dart'; // Import DioException
import 'package:cached_network_image/cached_network_image.dart'; // Import for cached images
import 'package:flutter_timezone/flutter_timezone.dart'; // Import flutter_timezone
// import 'dart:developer' as developer; // Import developer for logging (removed)
import '../../widgets/meal_card_widget.dart'; // Import the new MealCardWidget
import 'dart:async'; // Import for StreamSubscription
// import '../../services/auth_service.dart'; // Unused import
import '../../services/api_service.dart';
import '../../services/navigation_service.dart';
// import '../../config/api_config.dart'; // Unused import
import '../../models/user_profile.dart'; // Import UserProfile model
import '../../models/user_score.dart'; // Import UserScore model
import '../../models/score_history.dart'; // Import ScoreHistory model
import '../../widgets/workout_frame_widget.dart'; // Import the new WorkoutFrameWidget
import '../../models/workout_log.dart'; // Imports TodaysWorkoutResponse, WorkoutLog
import '../../models/workout.dart'; // Import WorkoutDay, WorkoutSection etc.
// Removed unused import
// Use prefix for meal_log models to avoid ambiguity
import '../../models/meal_log.dart' as meal_log_models
    show MealLog, TodaysMealResponse, Hydration; // Remove MealDetail from here
// import '../../providers/auth_provider.dart'; // No longer needed directly
import '../../services/auth_service.dart'; // Import AuthService
import '../../services/notification_service.dart'; // Import for notifications
// RESTORED import for HydrationLogDialog
import '../../widgets/hydration_log_dialog.dart'; // Import the new dialog
// Removed Logger import
import 'dart:math' as math;
import '../../utils/logger.dart'; // Import Logger
import 'dart:convert';
import 'dart:math' as num;
import 'package:intl/intl.dart'; // Import for date formatting

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  // Add a method to refresh data from outside
  void refreshData() {
    // Find the current state and call _loadData
    final state = _HomeScreenState.instance;
    if (state != null) {
      state._loadData();
    }
  }

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  // Add WidgetsBindingObserver
  // Static instance for access from widget
  static _HomeScreenState? instance;

  final ApiService _apiService = ApiService();
  // Removed _scaffoldKey as drawer is not used
  // final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // State variables using Models where applicable
  UserProfile? _userProfile; // Use UserProfile model
  TodaysWorkoutResponse? _workoutData; // Use the model
  meal_log_models.TodaysMealResponse? _mealData; // Use prefixed model
  UserScore? _userScore; // Use the model
  // Map<String, dynamic>? _hydrationData; // Removed, fetched via _mealData
  Map<String, dynamic>? _calorieData; // Keep as Map for now
  List<Map<String, dynamic>> _scoreHistory = []; // Store score history data
  bool _isLoading = true;
  bool _initialLoadComplete = false; // Flag for initial load
  DateTime now = DateTime.now();
  AuthService? _authService; // To hold the AuthService instance
  int _selectedIndex =
      0; // Index for bottom navigation (0=Home, 1=Workout, 3=Meals, 4=Profile)

  // Subscriptions for notifications
  StreamSubscription? _mealCompletedSubscription;
  StreamSubscription? _workoutCompletedSubscription;
  StreamSubscription? _workoutDataRefreshedSubscription;

  @override
  void initState() {
    super.initState();
    // Set the static instance
    instance = this;

    // Don't initialize with default values - let the API provide real data
    // We'll handle display of zeros in the UI instead

    // Listen for meal completion notifications with debouncing
    DateTime? lastMealCompletionTime;
    _mealCompletedSubscription =
        NotificationService.instance.on('meal_completed').listen((data) {
      // FIXED: Add debouncing to prevent multiple refreshes
      final now = DateTime.now();
      if (lastMealCompletionTime != null) {
        final timeDiff = now.difference(lastMealCompletionTime!);
        if (timeDiff.inMilliseconds < 500) {
          // Skip if less than 500ms since last refresh
          Logger.debug(
              "Skipping duplicate meal completion notification (${timeDiff.inMilliseconds}ms since last one)",
              tag: "HomeScreen");
          return;
        }
      }
      lastMealCompletionTime = now;

      // Reload data when a meal is completed, force refresh calories
      Logger.debug(
          "Processing meal completion notification. Calling _loadData(forceRefresh: true).",
          tag: "HomeScreen");
      _loadData(forceRefresh: true);
    });

    // Listen for workout completion notifications
    _workoutCompletedSubscription =
        NotificationService.instance.on('workout_completed').listen((data) {
      // Reload data when a workout is completed
      Logger.workout(
          'Workout completed notification received in HomeScreen with data: $data',
          tag: 'HomeScreen');
      _loadData();
    });

    // Listen for workout data refreshed notifications
    _workoutDataRefreshedSubscription =
        NotificationService.instance.on('workout_data_refreshed').listen((_) {
      // Reload data when workout data is refreshed
      Logger.workout(
          'Workout data refreshed notification received in HomeScreen',
          tag: 'HomeScreen');
      _loadData();
    });

    // Don't call _loadData directly here, wait for didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final authService = Provider.of<AuthService>(context, listen: false);
    // If _authService hasn't been set or changed, set it up
    if (_authService == null || _authService != authService) {
      _authService = authService;
      // Remove previous listener if any
      _authService?.removeListener(_handleAuthChange);
      // Add listener
      _authService?.addListener(_handleAuthChange);

      // Trigger initial load only once, after the frame is built AND if not already complete
      // This prevents the double load observed in logs
      if (!_initialLoadComplete) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_initialLoadComplete) {
            // Check completion flag again inside callback
            // Log that we are performing the initial load
            Logger.debug(
                "Performing initial _loadData from didChangeDependencies",
                tag: "HomeScreen");
            _loadData();
            // _initialLoadComplete will be set within _loadData upon success/failure
          }
        });
      } else {
        // If already loaded once, refresh calorie data when returning to the screen
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // Refresh only calories without forcing full refresh to keep UI responsive
            _apiService.getCalories(skipCache: true).then((data) {
              if (mounted && data['status'] == 'success') {
                setState(() {
                  _calorieData = data['data'];
                  Logger.debug(
                      "Updated calorie data on screen return: ${_calorieData?['consumed']}",
                      tag: "HomeScreen");
                });
              }
            });
          }
        });
      }
    }

    // Remove the secondary load check based on timestamp, as initial load handles it.
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (mounted) {
    //     // Check when we last loaded data
    //     final now = DateTime.now().millisecondsSinceEpoch;
    //     final lastLoadTime = _calorieData?[\'timestamp\'] as int? ?? 0;
    //
    //     // If it\'s been more than 30 seconds since the last load, refresh the data
    //     if (now - lastLoadTime > 30 * 1000) {
    //       _loadData();
    //     }
    //   }
    // });
  }

  @override
  void dispose() {
    // Clean up listener
    _authService?.removeListener(_handleAuthChange);

    // Cancel subscriptions
    _mealCompletedSubscription?.cancel();
    _workoutCompletedSubscription?.cancel();
    _workoutDataRefreshedSubscription?.cancel();

    // Clear the static instance if it's this instance
    if (instance == this) {
      instance = null;
    }

    super.dispose();
  }

  // Listener callback for AuthService changes
  void _handleAuthChange() {
    // AuthService notified HomeScreen
    // If the initial load is complete and the auth service notifies,
    // it might mean the profile was updated (e.g., after questionnaire).
    // Re-load data, ensuring it happens after the current frame build
    if (_initialLoadComplete && mounted) {
      // Initial load was complete, scheduling _loadData after frame
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Check mounted again inside callback
          _loadData();
        }
      });
    }
  }

  // Add optional forceRefresh parameter
  Future<void> _loadData({bool forceRefresh = false}) async {
    if (!mounted) return;
    // Set loading true ONLY if it's the very first load or a manual refresh
    if (!_initialLoadComplete) {
      setState(() => _isLoading = true);
    }
    Logger.debug(
        'HYDRATION: Starting _loadData. forceRefresh: $forceRefresh. Current _mealData value: ${_mealData?.hydration?.amountMl}',
        tag: 'HomeScreen'); // Enhanced log
    // setState(() => _isLoading = true); // Moved initial loading state set

    // --- Add Timezone Detection ---
    String? detectedTimezone;
    try {
      detectedTimezone = await FlutterTimezone.getLocalTimezone();
      Logger.debug('Detected local timezone: $detectedTimezone',
          tag: 'HomeScreen');
    } catch (e) {
      Logger.error('Failed to get local timezone: $e', tag: 'HomeScreen');
    }
    // --- End Timezone Detection ---

    UserProfile? fetchedUserProfile;
    UserScore? fetchedUserScore;
    TodaysWorkoutResponse? fetchedWorkoutData;
    meal_log_models.TodaysMealResponse? fetchedMealData; // Use prefixed model
    Map<String, dynamic>? fetchedCalorieData;
    Map<String, dynamic>? fetchedScoreHistory;

    try {
      // Get the current local date (still useful for the refresh trigger)
      final now = DateTime.now();
      final localDate = DateTime(now.year, now.month, now.day);
      Logger.debug(
          'Fetching data for user\'s current program day (Local Date: $localDate)',
          tag: 'HomeScreen');

      // Fetch profile, score, user meal plans (for target calories)
      final profileFuture = _apiService.getUserProfile();
      final scoreFuture = _apiService.getUserScore();
      final scoreHistoryFuture = _apiService.getUserScoreHistory();
      final userMealPlansFuture =
          _apiService.getUserMealPlans(); // Fetch plans for target

      // Fetch workout data using the /today/ endpoint
      final workoutFuture = _apiService.getTodayWorkout().catchError((e) {
        // REVERTED to getTodayWorkout
        if (e is DioException && e.response?.statusCode == 404) {
          Logger.debug('Caught 404 for getTodayWorkout, returning default',
              tag: 'HomeScreen');
          // Return a default/empty response instead of null
          return TodaysWorkoutResponse(
              // Ensure this matches the expected return type
              date: DateTime.now().toIso8601String().split('T')[0],
              workoutLogs: [],
              isRestDay: true, // Assume rest day if no plan found
              nextWorkoutDate: null);
        }
        // Error fetching workout data (non-404)
        throw e; // Rethrow other errors
      });

      // Fetch meal data using the /today/ endpoint, skip cache
      final mealFuture = _apiService.getTodayMeals().catchError((e) {
        // REVERTED to getTodayMeals, REMOVED skipCache: true
        if (e is DioException && e.response?.statusCode == 404) {
          Logger.debug('Caught 404 for getTodayMeals, returning default',
              tag: 'HomeScreen');
          // Return a default/empty response instead of null
          // Use prefixed Hydration constructor
          return meal_log_models.TodaysMealResponse(
              // Ensure this matches the expected return type
              date: DateTime.now().toIso8601String().split('T')[0],
              mealLogs: [],
              hydration:
                  meal_log_models.Hydration(amountMl: 0, targetMl: 2500));
        }
        // Error fetching meal data (non-404)
        throw e; // Rethrow other errors
      });

      // Add calorie data future - use cache for initial load to prevent flicker
      // Only skip cache if this is a forced refresh or we don't have data yet
      final caloriesFuture = _apiService.getCalories(
          skipCache: !(forceRefresh || _calorieData == null));

      // Wait for all futures
      final results = await Future.wait([
        profileFuture,
        scoreFuture,
        workoutFuture, // Future already includes error handling
        mealFuture, // Future already includes error handling
        userMealPlansFuture, // Add future for meal plans
        caloriesFuture, // Add calories future
        scoreHistoryFuture, // Add score history future
      ]);

      // Assign results after checking if mounted
      if (!mounted) return;

      // Set initial load complete flag HERE, before setting state,
      // to prevent re-entry from didChangeDependencies if setState triggers it.
      _initialLoadComplete = true;

      // Safely cast results
      fetchedUserProfile = results[0] as UserProfile?;
      fetchedUserScore = results[1] as UserScore?;
      fetchedWorkoutData = results[2] as TodaysWorkoutResponse?;
      fetchedMealData = results[3] as meal_log_models.TodaysMealResponse?;
      final fetchedUserMealPlans = results[4] as List<Map<String, dynamic>>?;
      final rawCalorieResponse =
          results[5] as Map<String, dynamic>?; // Get raw calorie response

      // --- Extract actual calorie data from nested structure --- START
      Map<String, dynamic>? extractedCalorieData;
      if (rawCalorieResponse != null &&
          rawCalorieResponse['status'] == 'success' &&
          rawCalorieResponse['data'] is Map) {
        extractedCalorieData =
            rawCalorieResponse['data'] as Map<String, dynamic>;
        // Ensure numeric types (Backend might send strings sometimes)
        extractedCalorieData = extractedCalorieData.map((key, value) {
          if (value is String) {
            // Try parsing as int first, then double
            final intVal = int.tryParse(value);
            if (intVal != null) {
              return MapEntry(key, intVal);
            }
            final doubleVal = double.tryParse(value);
            return MapEntry(
                key,
                doubleVal ??
                    value); // Keep original string if neither parse works
          } else if (value is int || value is double) {
            // Explicitly check int OR double
            return MapEntry(key, value); // Keep numbers as they are
          } else {
            return MapEntry(key, value); // Keep other types
          }
        });
      } else {
        // Handle error or missing data case - log and use defaults
        // Corrected Logger call syntax:
        Logger.warning(
            'Could not extract calorie data from response (status=${rawCalorieResponse?['status']}). Using defaults.',
            tag: 'HomeScreen');
        // Use the default map directly from ApiService (assuming it's accessible or recreated here)
        extractedCalorieData = {
          'consumed': 0,
          'burned': 0,
          'target': 2200, // Default target
          'net': 0,
          'remaining': 2200,
          'weekly_average': 0,
          'monthly_average': 0,
        };
      }
      // --- Extract actual calorie data from nested structure --- END

      // Log the raw result specifically
      Logger.log('RAW calorie API response: ${results[5]}', tag: 'HomeScreen');
      // Log the extracted data
      Logger.log('Extracted calorie data in _loadData: $extractedCalorieData',
          tag: 'HomeScreen');
      fetchedScoreHistory =
          results[6] as Map<String, dynamic>?; // Get score history data

      // Log fetched calorie data, especially during forced refresh
      if (forceRefresh) {
        // Log from extracted data
        Logger.debug(
            "Forced Refresh: Extracted calories consumed: ${extractedCalorieData['consumed']}",
            tag: "HomeScreen");
      }

      // Log the fetched hydration data BEFORE setState
      Logger.debug(
          'HYDRATION: Fetched meal data. Hydration amount: ${fetchedMealData?.hydration?.amountMl}',
          tag: 'HomeScreen');
      Logger.debug(
          'MEALS: Fetched meal logs count: ${fetchedMealData?.mealLogs.length ?? 0}',
          tag: 'HomeScreen');
      if (fetchedMealData?.mealLogs.isNotEmpty ?? false) {
        Logger.debug(
            'MEALS: First fetched meal: ${fetchedMealData!.mealLogs.first.meal.name}',
            tag: 'HomeScreen');
      }

      // --- Check and Update Timezone IF NEEDED ---
      if (fetchedUserProfile != null &&
          detectedTimezone != null &&
          detectedTimezone.isNotEmpty) {
        final String currentStoredTimezone =
            fetchedUserProfile.timezone ?? 'UTC';
        if (currentStoredTimezone != detectedTimezone) {
          Logger.info(
              'Stored timezone ($currentStoredTimezone) differs from detected ($detectedTimezone). Updating profile...',
              tag: 'HomeScreen');
          try {
            // Call updateUserProfile (renamed method) with only the data map
            await _apiService.updateUserProfile(
                /*fetchedUserProfile.id,*/ {'timezone': detectedTimezone});
            // Update the local profile object immediately after successful API call
            fetchedUserProfile =
                fetchedUserProfile.copyWith(timezone: detectedTimezone);
            Logger.info('Profile timezone updated successfully on backend.',
                tag: 'HomeScreen');
          } catch (e) {
            Logger.error('Failed to update profile timezone on backend: $e',
                tag: 'HomeScreen');
            // Don't block UI update, just log the error
          }
        }
      }
      // --- End Check and Update Timezone ---

      // Now update the state with potentially updated fetchedUserProfile
      setState(() {
        _userProfile = fetchedUserProfile;
        _userScore = fetchedUserScore;
        _workoutData = fetchedWorkoutData;
        _mealData = fetchedMealData;
        _calorieData = extractedCalorieData; // <<< Use the EXTRACTED data
        // Process score history before setting state
        _scoreHistory = _processScoreHistory(fetchedScoreHistory);
        // _isLoading = false; // Moved to finally block
        // Log the state variable AFTER setState
        Logger.log('Updated _calorieData state variable: $_calorieData',
            tag: 'HomeScreen');
      });

      // Log updated calorie state, especially during forced refresh
      if (forceRefresh) {
        // Log from state data
        Logger.debug(
            "Forced Refresh: Updated _calorieData state consumed: ${_calorieData?['consumed']}",
            tag: "HomeScreen");
      }

      // Data fetched successfully
    } catch (e, stackTrace) {
      // Catch stack trace
      if (!mounted) return;
      // Set initial load complete even on error to prevent reload loops
      _initialLoadComplete = true;
      Logger.error('Error loading home screen data: $e',
          error: e, stackTrace: stackTrace, tag: 'HomeScreen');
      // Optionally show a snackbar or error message
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(content: Text('Failed to load data. Please try again.')),
      // );
    } finally {
      if (mounted) {
        // Ensure loading indicator is turned off regardless of success/failure
        // but only if it was turned on initially.
        // Use _isLoading check to avoid unnecessary setState calls on background refreshes.
        if (_isLoading) {
          setState(() => _isLoading = false);
        }
      }
      Logger.debug(
          'HYDRATION: Finished _loadData. New state value: ${_mealData?.hydration?.amountMl}',
          tag: 'HomeScreen'); // Enhanced log
    }
  }

  void _onItemTapped(int index) {
    // Map UI index (0, 1, 2, 3) back to internal state index (0, 1, 3, 4)
    int targetIndex = index;
    if (index >= 2) {
      targetIndex = index + 1;
    }

    if (_selectedIndex == targetIndex) return; // Avoid unnecessary rebuilds

    setState(() {
      _selectedIndex = targetIndex;
    });

    // Handle navigation based on the internal state index
    switch (targetIndex) {
      case 0:
        // Already on Home, do nothing or refresh?
        break;
      case 1:
        NavigationService.navigateToReplacementNamed(
          NavigationService.main,
          arguments: {'initialTabIndex': 1},
        );
        break;
      // case 2: // This index is skipped
      //   break;
      case 3:
        NavigationService.navigateToNamed('/meal-plan');
        break;
      case 4:
        NavigationService.navigateToNamed('/profile');
        break;
    }
  }

  // Updated _completeWorkout to use models and refresh data
  Future<void> _completeWorkout(
      int workoutLogId, int? workoutSessionLogId) async {
    // Added workoutSessionLogId
    // Parameter changed to int
    if (!mounted) return;
    // Attempting to complete workout log ID
    if (workoutLogId <= 0) {
      // Ensure workoutLogId is valid
      Logger.error(
          'Cannot complete workout: Invalid Workout Log ID ($workoutLogId)',
          tag: 'HomeScreen');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Cannot complete workout: Missing workout information.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      // First, complete the individual workout session if available
      if (workoutSessionLogId != null && workoutSessionLogId > 0) {
        try {
          await _apiService.completeWorkoutSessionLog(
              workoutSessionLogId: workoutSessionLogId);
          Logger.workout(
              'Successfully completed workout session: $workoutSessionLogId',
              tag: 'HomeScreen');
        } catch (e) {
          Logger.error('Error completing workout session: $e',
              tag: 'HomeScreen');
          // Continue with workout log completion even if session completion fails
        }
      }

      // Then complete the entire workout log
      await _apiService.completeWorkoutLog(workoutLogId: workoutLogId);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Workout completed successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        // Refresh data to show updated workout status and potentially score
        _loadData();
      }
    } catch (e) {
      // Error completing workout log
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete workout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // --- Hydration Logging (RESTORED - Using HydrationLogDialog) ---
  Future<void> _showLogHydrationDialog() async {
    // Get current and target amounts from state
    final int currentAmount = _mealData?.hydration?.amountMl ?? 0;
    final int targetAmount = _mealData?.hydration?.targetMl ?? 2500;

    Logger.debug('HYDRATION: Current amount before dialog: $currentAmount',
        tag: 'HomeScreen');

    // Show the new custom dialog
    final amountToLog = await showDialog<int>(
      context: context,
      builder: (BuildContext context) {
        // Pass current and target amounts to the dialog
        return HydrationLogDialog(
          currentAmountMl: currentAmount,
          targetAmountMl: targetAmount,
        );
      },
    );

    // Handle the result from the dialog
    if (amountToLog != null && amountToLog > 0) {
      Logger.debug('HYDRATION: Amount to log from dialog: $amountToLog',
          tag: 'HomeScreen');
      try {
        // Calculate new total amount (API expects the *new total*, not just the amount added)
        final newTotalAmount = currentAmount + amountToLog;
        Logger.debug('HYDRATION: Calling API with new total: $newTotalAmount',
            tag: 'HomeScreen');
        await _apiService.logHydration(amountMl: newTotalAmount);
        Logger.debug('HYDRATION: API call returned successfully',
            tag: 'HomeScreen');

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Logged $amountToLog ml of water!'),
              backgroundColor: Colors.green,
            ),
          );
          // Refresh data to show updated hydration
          _loadData();
        }
      } catch (e) {
        // Error logging hydration
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to log hydration: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      // Hydration logging cancelled or amount was zero
    }
  }
  // --- End Hydration Logging ---

  // Check if the questionnaire has been completed
  bool _isQuestionnaireCompleted() {
    // Check if user profile has essential fields filled
    if (_userProfile == null) return false;

    // Check if we have workout or meal data - this is a good indicator that plans have been assigned
    bool hasWorkoutData =
        _workoutData != null && !(_workoutData?.noPlanAssigned ?? true);
    bool hasMealData =
        _mealData != null && (_mealData?.mealLogs.isNotEmpty ?? false);

    // If we have either workout or meal data, consider the questionnaire completed
    if (hasWorkoutData || hasMealData) {
      return true;
    }

    // Check if we have a Sandow Score - this is a good indicator that the questionnaire was completed
    if (_userScore != null &&
        _userScore!.totalScore != null &&
        _userScore!.totalScore! > 0) {
      return true;
    }

    // Check for essential profile fields that would be filled during questionnaire
    // If ALL essential fields are filled, consider the questionnaire completed
    if (_userProfile!.gender != null &&
        _userProfile!.height != null &&
        _userProfile!.weight != null &&
        _userProfile!.fitnessGoal != null &&
        _userProfile!.fitnessLevel != null) {
      return true;
    }

    // If we have a non-zero calorie target, consider the questionnaire completed
    if (_calorieData != null && (_calorieData?['target'] ?? 0) > 0) {
      return true;
    }

    // If none of the above conditions are met, the questionnaire is not completed
    return false;
  }

  // Navigate to questionnaire
  void _navigateToQuestionnaire() {
    NavigationService.navigateToNamed(NavigationService.questionnaire);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    // Read AuthService once without listening for rebuilds here
    final authService = Provider.of<AuthService>(context, listen: false);

    // Check if questionnaire is completed after data is loaded
    if (!_isLoading && !_isQuestionnaireCompleted()) {
      // Show dialog to complete questionnaire
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Show a snackbar instead of a dialog
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                  'Complete your profile to get personalized workout and meal plans'),
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Complete Now',
                onPressed: () {
                  _navigateToQuestionnaire();
                },
              ),
            ),
          );
        }
      });
    }

    // We don't need to map the index here since we're not using bottom navigation in this screen

    return Scaffold(
      // key: _scaffoldKey, // Removed key as drawer is not used
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: Color(0xFFF97316)))
            : SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Container(
                  width: screenWidth,
                  // Removed fixed height and redundant decoration
                  color: Colors.white,
                  child: Stack(
                    children: [
                      // Header Section
                      _buildHeader(authService), // Pass authService instance

                      // Main Content
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 214), // Adjust based on header height
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Add extra spacing before Fitness Metrics Section
                            const SizedBox(height: 24),
                            // Fitness Metrics Section
                            _buildFitnessMetrics(),
                            const SizedBox(height: 24),

                            // Workouts Section
                            _buildWorkoutsSection(
                                _workoutData), // Pass the _workoutData state variable
                            const SizedBox(height: 24),

                            // Diet & Nutrition Section
                            _buildNutritionSection(),
                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
      // Removed the duplicate BottomNavigationBar from HomeScreen
    );
  }

  // Accept AuthService as a parameter
  Widget _buildHeader(AuthService authService) {
    final screenWidth = MediaQuery.of(context).size.width;
    // No longer listen here, use the passed instance

    // --- Use fetched data from state variables ---
    // Get first name from AuthService.currentUser, fallback to username or "User"
    final firstName = authService.currentUser?.firstName.isNotEmpty ?? false
        ? authService.currentUser!.firstName
        : (authService.currentUser?.username.isNotEmpty ?? false
            ? authService.currentUser!.username // Fallback to username
            : 'User'); // Final fallback

    // Get profile picture URL ONLY from _userProfile (as AuthService doesn't store it)
    final profileImageUrl = _userProfile?.profile_picture;
    // --- End of using fetched data ---

    // Extract score data safely using the UserScore object
    final totalScore =
        _userScore?.totalScore ?? 0; // Keep using state variable for score

    return Container(
      width: screenWidth,
      height: 214, // Keep header height consistent
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(32)), // Rounded bottom corners
        // Add image directly to the main container decoration
        image: DecorationImage(
          image: AssetImage('assets/images/homebg.png'),
          fit: BoxFit.cover,
          // Add a color filter to ensure text remains visible
          colorFilter: ColorFilter.mode(
            Color.fromRGBO(16, 17, 20, 0.8), // Semi-transparent dark overlay
            BlendMode.srcOver,
          ),
        ),
      ),
      child: Stack(
        children: [
          // Remove the old positioned background image

          // Date display
          Positioned(
            top: 58,
            left: 16,
            child: Text(
              '${_getMonthName(now.month)} ${now.day}, ${now.year}',
              style: const TextStyle(
                color: Color.fromRGBO(255, 255, 255, 0.64),
                fontFamily: 'Work Sans',
                fontSize: 12,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),

          // Settings Button - Keep this from the original design
          Positioned(
            top: 58,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18),
                color: const Color.fromRGBO(35, 37, 43, 1),
              ),
              child: IconButton(
                icon: const Icon(Icons.settings_outlined, color: Colors.white),
                onPressed: () => NavigationService.navigateToNamed('/settings'),
              ),
            ),
          ),

          // User Info and Score
          Positioned(
            top: 114,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Profile Picture
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                  child: ClipOval(
                    child: profileImageUrl != null && profileImageUrl.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: profileImageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white.withValues(
                                        red: 255,
                                        green: 255,
                                        blue: 255,
                                        alpha: 128)),
                                strokeWidth: 2,
                              ),
                            ),
                            errorWidget: (context, url, error) => const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 32,
                            ),
                          )
                        : const Icon(Icons.person,
                            color: Colors.white, size: 32),
                  ),
                ),
                const SizedBox(width: 12),
                // User Name and Score/Membership
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Hello, $firstName!',
                        style: const TextStyle(
                          color: Colors.white,
                          fontFamily: 'Work Sans',
                          fontSize: 30,
                          fontWeight: FontWeight.bold,
                          height: 1.27,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.favorite,
                              color: Colors.redAccent, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            '$totalScore% Healthy',
                            style: const TextStyle(
                              color: Colors.white,
                              fontFamily: 'Work Sans',
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Process score history data from API response
  List<Map<String, dynamic>> _processScoreHistory(
      Map<String, dynamic>? scoreHistoryData) {
    List<Map<String, dynamic>> result = [];

    if (scoreHistoryData != null &&
        scoreHistoryData.containsKey('history') &&
        scoreHistoryData['history'] is List) {
      // Convert the history data to a list of maps
      result = List<Map<String, dynamic>>.from(scoreHistoryData['history']
          .map((item) => item as Map<String, dynamic>));

      // Sort by date (newest first)
      result
          .sort((a, b) => (b['date'] as String).compareTo(a['date'] as String));

      // Take only the last 7 days (or fewer if not available)
      if (result.length > 7) {
        result = result.sublist(0, 7);
      }

      // Reverse to get oldest first (for graph display)
      result = result.reversed.toList();
    }

    // If no data or empty, return an empty list
    return result;
  }

  Widget _buildFitnessMetrics() {
    // Ensure we have valid data for all metrics
    final hydrationAmount = _mealData?.hydration?.amountMl ?? 0;
    final hydrationTarget = _mealData?.hydration?.targetMl ?? 2500;
    Logger.debug(
        'HYDRATION: Building hydration card with amount: $hydrationAmount',
        tag: 'HomeScreen'); // Log value used in UI

    // If _calorieData is null, trigger a refresh but don't use default values
    if (_calorieData == null) {
      // Schedule a refresh of calorie data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _apiService.getCalories(skipCache: true).then((data) {
            if (mounted) {
              setState(() {
                _calorieData = data;
                // Removed debug logging
              });
            }
          });
        }
      });
    }

    // Use safe values with fallbacks
    final caloriesConsumed = _calorieData?['consumed'] ?? 0;
    // Removed unused variable
    final calorieTarget = _calorieData?['target'] ?? 2200;
    final totalScore = _userScore?.totalScore ?? 0;

    // Add logging here to check the value used in UI
    Logger.log('Building Fitness Metrics with _calorieData: $_calorieData',
        tag: 'HomeScreen');

    // Removed debug logging

    // Get score history for the graph
    List<double> weeklyScores = [];

    // Use real score history data if available
    if (_scoreHistory.isNotEmpty) {
      weeklyScores = _scoreHistory.map((entry) {
        final scoreValue = entry['score'];
        if (scoreValue is double) {
          return scoreValue;
        } else if (scoreValue is int) {
          return scoreValue.toDouble();
        } else {
          // Handle unexpected type or null, default to 0.0
          return 0.0;
        }
      }).toList();

      // If we have fewer than 7 days of data, pad with the current score
      while (weeklyScores.length < 7) {
        weeklyScores.add(totalScore.toDouble());
      }
    } else if (_userScore != null) {
      // Fallback to generated data if no history but we have a score
      final baseScore = totalScore.toDouble();
      weeklyScores = [
        (baseScore - 10).clamp(0.0, 100.0),
        (baseScore - 5).clamp(0.0, 100.0),
        (baseScore - 2).clamp(0.0, 100.0),
        (baseScore + 3).clamp(0.0, 100.0),
        (baseScore - 1).clamp(0.0, 100.0),
        (baseScore + 2).clamp(0.0, 100.0),
        baseScore,
      ];
    } else {
      weeklyScores = [65, 70, 75, 80, 85, 88, 90]; // Default data
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Fitness Metrics',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF101114),
                  fontFamily: 'Work Sans',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height:
                200, // Further increased height to accommodate cards properly
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Score Card - Wrap with GestureDetector
                  GestureDetector(
                    onTap: _showScoreBreakdown,
                    child: _buildNewScoreCard(totalScore, weeklyScores),
                  ),
                  const SizedBox(width: 16),
                  // Hydration Card - Wrap with GestureDetector, use _showLogHydrationDialog
                  GestureDetector(
                    onTap:
                        _showLogHydrationDialog, // CORRECTED: Point to the restored function
                    child: _buildNewHydrationCard(
                        hydrationAmount, hydrationTarget),
                  ),
                  const SizedBox(width: 16),
                  // Calorie Card - Wrap with GestureDetector
                  GestureDetector(
                    onTap: _showCalorieDetails,
                    child:
                        _buildNewCalorieCard(caloriesConsumed, calorieTarget),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showScoreBreakdown() {
    // Show a bottom sheet with score breakdown instead of navigating to a new screen
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.85,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (_, controller) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Score Breakdown',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  controller: controller,
                  children: [
                    _buildScoreBreakdownContent(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScoreBreakdownContent() {
    // Get actual score values from the UserScore object
    final totalScore = _userScore?.totalScore ?? 0;
    final workoutScore = _userScore?.workoutScore ?? 0;
    final streakScore = _userScore?.streakScore ?? 0;
    final nutritionScore = _userScore?.nutritionScore ?? 0;
    final goalScore = _userScore?.goalScore ?? 0;

    // Score Breakdown - Total: $totalScore, Workout: $workoutScore, Streak: $streakScore, Nutrition: $nutritionScore, Goal: $goalScore

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: const Color(0xFFFF7F36),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              const Text(
                'Your Health Score',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Work Sans',
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '$totalScore',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  const Text(
                    '%',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        const Text(
          'Score Components',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            fontFamily: 'Work Sans',
          ),
        ),
        const SizedBox(height: 16),
        // Use actual score values from the UserScore object
        _buildScoreComponent('Workout Score', workoutScore, 0.4),
        _buildScoreComponent('Streak Score', streakScore, 0.2),
        _buildScoreComponent('Nutrition Score', nutritionScore, 0.3),
        _buildScoreComponent('Goal Score', goalScore, 0.1),
      ],
    );
  }

  Widget _buildScoreComponent(String label, int value, double weight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Work Sans',
                ),
              ),
              Text(
                '$value',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Work Sans',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: value / 100,
            backgroundColor: Colors.grey[200],
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFF7F36)),
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
          const SizedBox(height: 8),
          Text(
            'Weight: ${(weight * 100).toInt()}%',
            style: TextStyle(
              fontSize: 14,
              fontFamily: 'Work Sans',
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  void _showCalorieDetails() {
    // Ensure we have valid calorie data
    if (_calorieData == null) {
      // If _calorieData is null, fetch it again
      _apiService.getCalories(skipCache: true).then((data) {
        if (mounted) {
          setState(() {
            _calorieData = data;
            // Show the details after fetching data
            _showCalorieDetailsInternal();
          });
        }
      }).catchError((e) {
        // If fetching fails, use empty values (not defaults)
        if (mounted) {
          setState(() {
            _calorieData = {
              'consumed': 0,
              'burned': 0,
              'target': 2200, // Default target is reasonable
              'net': 0,
              'remaining': 2200,
              'weekly_average': 0,
              'monthly_average': 0,
            };
            // Show the details with empty data
            _showCalorieDetailsInternal();
          });
        }
      });
      return;
    }

    // If we have data, show the details directly
    _showCalorieDetailsInternal();
  }

  void _showCalorieDetailsInternal() {
    // Get calorie data from the state
    final caloriesConsumed = _calorieData?['consumed'] ?? 0;
    final caloriesBurned = _calorieData?['burned'] ?? 0;
    final calorieTarget = _calorieData?['target'] ?? 2200;
    final caloriesRemaining = _calorieData?['remaining'] ??
        (calorieTarget - caloriesConsumed + caloriesBurned);

    // Removed debug logging

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        builder: (_, controller) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Calories',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  controller: controller,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: const Color(0xFF676B74),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 120,
                            width: 120,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                SizedBox(
                                  height: 120,
                                  width: 120,
                                  child: CircularProgressIndicator(
                                    value: calorieTarget > 0
                                        ? (caloriesConsumed / calorieTarget)
                                            .clamp(0.0, 1.0)
                                        : 0.0,
                                    backgroundColor: Colors.white.withAlpha(50),
                                    valueColor:
                                        const AlwaysStoppedAnimation<Color>(
                                            Colors.white),
                                    strokeWidth: 10,
                                  ),
                                ),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      '$caloriesConsumed',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 28,
                                        fontWeight: FontWeight.bold,
                                        fontFamily: 'Work Sans',
                                      ),
                                    ),
                                    const Text(
                                      'kcal',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontFamily: 'Work Sans',
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Target: $calorieTarget kcal',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontFamily: 'Work Sans',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Remaining: $caloriesRemaining kcal',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Work Sans',
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Today\'s Summary',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Work Sans',
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildCalorieSummaryCard(
                            'Consumed',
                            '$caloriesConsumed kcal',
                            Icons.restaurant,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildCalorieSummaryCard(
                            'Burned',
                            '$caloriesBurned kcal',
                            Icons.local_fire_department,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildCalorieSummaryCard(
                            'Net',
                            '${caloriesConsumed - caloriesBurned} kcal',
                            Icons.calculate,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalorieSummaryCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: const Color(0xFF676B74),
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontFamily: 'Work Sans',
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: 'Work Sans',
              color: Color(0xFF101114),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewScoreCard(int score, List<double> weeklyScores) {
    return Container(
      width: 154, // Fixed width for consistent sizing
      height: 180, // Fixed height to prevent overflow
      padding: const EdgeInsets.all(16), // Padding from example
      decoration: BoxDecoration(
        color: const Color(0xFFFF7F36),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Score',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Work Sans',
                  fontSize: 14, // Reduced font size
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.show_chart,
                  color: Colors.white.withAlpha(178), size: 16), // Smaller icon
            ],
          ),
          const SizedBox(height: 8), // Reduced spacing to fit in fixed height
          // Center the graph
          Center(
            child: SizedBox(
              height: 70, // Reduced height to fit in fixed container
              width: 122,
              child: CustomPaint(
                painter: ScoreGraphPainter(weeklyScores),
              ),
            ),
          ),
          const Spacer(), // Push the score to the bottom
          // Center the score and make it bigger
          Center(
            child: Text(
              '$score',
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Work Sans',
                fontSize: 32, // Increased font size
                fontWeight: FontWeight.bold,
                height: 1.2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewHydrationCard(int amount, int target) {
    final progress = target > 0 ? (amount / target).clamp(0.0, 1.0) : 0.0;

    return Container(
      width: 154, // Fixed width for consistent sizing
      height: 180, // Fixed height to prevent overflow
      padding: const EdgeInsets.all(16), // Padding from example
      decoration: BoxDecoration(
        color: const Color(0xFF246CD0),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Hydration',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Work Sans',
                  fontSize: 14, // Reduced font size
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.water_drop,
                  color: Colors.white.withAlpha(178),
                  size: 16), // Even smaller icon
            ],
          ),
          const SizedBox(height: 8), // Reduced spacing to fit in fixed height
          // Center the progress circle
          Center(
            child: SizedBox(
              height: 70, // Reduced height to fit in fixed container
              width: 122,
              child: CustomPaint(
                painter: SmoothCircularProgressPainter(
                  progress: progress,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const Spacer(), // Push the value to the bottom
          // Center the value and make it bigger
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  '$amount',
                  style: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Work Sans',
                    fontSize: 32, // Increased font size
                    fontWeight: FontWeight.bold,
                    height: 1.2,
                  ),
                ),
                const SizedBox(width: 4), // Slightly more spacing
                const Text(
                  'ml',
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: 'Work Sans',
                    fontSize: 18, // Slightly increased font size
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewCalorieCard(int consumed, int target) {
    final progress = target > 0 ? (consumed / target).clamp(0.0, 1.0) : 0.0;

    return Container(
      width: 154, // Fixed width for consistent sizing
      height: 180, // Fixed height to prevent overflow
      padding: const EdgeInsets.all(16), // Padding from example
      decoration: BoxDecoration(
        color: const Color(0xFF676B74),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Calorie',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Work Sans',
                  fontSize: 14, // Reduced font size
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.local_fire_department,
                  color: Colors.white.withAlpha(178),
                  size: 16), // Even smaller icon
            ],
          ),
          const SizedBox(height: 8), // Reduced spacing to fit in fixed height
          // Center the progress circle
          Center(
            child: SizedBox(
              height: 70, // Reduced height to fit in fixed container
              width: 122,
              child: CustomPaint(
                painter: SmoothCircularProgressPainter(
                  progress: progress,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const Spacer(), // Push the value to the bottom
          // Center the value and make it bigger
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  '$consumed',
                  style: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Work Sans',
                    fontSize: 32, // Increased font size
                    fontWeight: FontWeight.bold,
                    height: 1.2,
                  ),
                ),
                const SizedBox(width: 4), // Slightly more spacing
                const Text(
                  'kcal',
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: 'Work Sans',
                    fontSize: 18, // Slightly increased font size
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreBreakdownItem(String label, int score) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0), // Adjusted padding
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontFamily: 'Work Sans',
              color: Color(0xFF393B42), // Slightly darker text
            ),
          ),
          Text(
            '$score', // Display score value
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600, // Semi-bold
              fontFamily: 'Work Sans',
              color: Color(0xFF101114),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHydrationCard(int amount, int target) {
    final percentage = target > 0 ? (amount / target).clamp(0.0, 1.0) : 0.0;

    return Container(
      width: 130, // Reduced width
      // height: 130, // Let content define height
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20), // Slightly less rounding
        color: const Color(0xFF246CD0), // Hydration color
      ),
      padding: const EdgeInsets.all(12), // Reduced padding
      child: Column(
        crossAxisAlignment:
            CrossAxisAlignment.center, // Center content horizontally
        mainAxisAlignment:
            MainAxisAlignment.center, // Center content vertically
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Hydration',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Work Sans',
                ),
              ),
              Icon(Icons.info_outline,
                  // Removed onTap from here
                  color: Colors.white.withOpacity(0.7),
                  size: 18),
            ],
          ),
          const SizedBox(height: 8), // Reduced spacing
          SizedBox(
            width: 50, // Reduced ring size
            height: 50, // Reduced ring size
            child: Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 50, // Match SizedBox
                  height: 50, // Match SizedBox
                  child: CustomPaint(
                    painter: ScoreRingPainter(
                      progress: percentage,
                      color: Colors.white,
                    ),
                  ),
                ),
                Icon(
                  Icons.water_drop,
                  size: 24, // Reduced icon size
                  color: Colors.white.withOpacity(0.8),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8), // Reduced spacing
          // Hydration Text
          Column(
            crossAxisAlignment: CrossAxisAlignment.center, // Center text
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '$amount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28, // Reduced size
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  const Text(
                    'ml',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                'Target: ${target}ml',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                  fontFamily: 'Work Sans',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Update to accept target
  Widget _buildCalorieCard(int caloriesConsumed, int calorieTarget) {
    // Use calorie data map safely for averages
    final int weeklyAverage = _calorieData?['weekly_average'] ?? 0;
    final int monthlyAverage = _calorieData?['monthly_average'] ?? 0;
    // Use passed target
    final double progress = calorieTarget > 0
        ? (caloriesConsumed / calorieTarget).clamp(0.0, 1.0)
        : 0.0;

    return Container(
      width: 130, // Keep reduced width
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20), // Keep slightly less rounding
        color: const Color(0xFF676B74), // Calories color
      ),
      padding: const EdgeInsets.all(12), // Keep reduced padding
      child: Column(
        crossAxisAlignment:
            CrossAxisAlignment.center, // Keep centered content horizontally
        mainAxisAlignment:
            MainAxisAlignment.center, // Center content vertically
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Calories',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Work Sans',
                ),
              ),
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    isScrollControlled: true,
                    builder: (context) => _buildCalorieBreakdownSheet(
                        caloriesConsumed, // Correct variable
                        weeklyAverage,
                        monthlyAverage,
                        calorieTarget), // Correct variable
                  );
                },
                child: Icon(
                  Icons.info_outline,
                  color: Colors.white.withOpacity(0.7),
                  size: 18, // Keep size
                ),
              ),
            ],
          ),
          const SizedBox(height: 8), // Keep reduced spacing
          SizedBox(
            width: 50, // Keep reduced ring size
            height: 50, // Keep reduced ring size
            child: Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 50, // Match SizedBox
                  height: 50, // Match SizedBox
                  child: CustomPaint(
                    painter: ScoreRingPainter(
                      progress: progress,
                      color: Colors.white,
                    ),
                  ),
                ),
                Icon(
                  Icons.local_fire_department,
                  size: 24, // Keep reduced icon size
                  color: Colors.white.withOpacity(0.8),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8), // Keep reduced spacing
          // Calorie Text
          Column(
            crossAxisAlignment: CrossAxisAlignment.center, // Keep centered
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '$caloriesConsumed', // Keep consumed calories
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28, // Keep reduced size
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  const Text(
                    'kcal',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18, // Keep size
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                'Goal: ${calorieTarget}kcal', // Display target
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                  fontFamily: 'Work Sans',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Update signature
  Widget _buildCalorieBreakdownSheet(
      int consumed, int weeklyAvg, int monthlyAvg, int target) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Calorie Breakdown',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              fontFamily: 'Work Sans',
              color: Color(0xFF101114),
            ),
          ),
          const SizedBox(height: 20),
          _buildCalorieBreakdownItem(
              'Today Consumed', consumed), // Update label
          _buildCalorieBreakdownItem('Weekly Average', weeklyAvg),
          _buildCalorieBreakdownItem('Monthly Average', monthlyAvg),
          const Divider(height: 32, thickness: 1),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Daily Goal',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Work Sans',
                  color: Color(0xFF101114),
                ),
              ),
              Text(
                '$target kcal', // Use target
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14, // Reduced size
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Work Sans',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCalorieBreakdownItem(String label, int calories) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontFamily: 'Work Sans',
              color: Color(0xFF393B42),
            ),
          ),
          Text(
            '$calories kcal',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              fontFamily: 'Work Sans',
              color: Color(0xFF101114),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutsSection(TodaysWorkoutResponse? todaysWorkoutData) {
    // Determine if there's a workout for today
    final bool isRestDay = todaysWorkoutData?.isRestDay ?? true;
    final bool noPlanAssigned = todaysWorkoutData?.noPlanAssigned ?? true;
    final bool hasWorkoutLogs =
        todaysWorkoutData?.workoutLogs.isNotEmpty ?? false;
    final bool hasSessions = todaysWorkoutData?.sessions?.isNotEmpty ?? false;

    // A workout exists if it's not a rest day, a plan is assigned, and there are either logs or sessions
    final bool hasWorkout =
        !isRestDay && !noPlanAssigned && (hasWorkoutLogs || hasSessions);

    WorkoutLog? firstWorkoutLog;
    WorkoutDay? workoutDay;
    String workoutName = '';
    int? duration = 0; // No default duration
    int? calories = 0; // No default calories
    String? coverImageUrl;
    bool isTodayWorkoutCompleted = false;

    // Debug logging to see what's available
    Logger.meal('WorkoutData available: ${todaysWorkoutData != null}',
        tag: 'HomeScreen');
    if (todaysWorkoutData != null) {
      Logger.meal(
          'WorkoutDay name from API: ${todaysWorkoutData.workoutDayName}',
          tag: 'HomeScreen');
      Logger.meal(
          'Has workout logs: $hasWorkoutLogs, Has sessions: $hasSessions',
          tag: 'HomeScreen');
      if (hasWorkoutLogs) {
        Logger.meal(
            'First workout log name: ${todaysWorkoutData.workoutLogs.first.workoutDay?.name}',
            tag: 'HomeScreen');
      }

      // Set the workout name from the API response if available
      if (todaysWorkoutData.workoutDayName != null &&
          todaysWorkoutData.workoutDayName!.isNotEmpty) {
        workoutName = todaysWorkoutData.workoutDayName!;
        Logger.meal('Using workout day name from API: $workoutName',
            tag: 'HomeScreen');
      } else if (todaysWorkoutData.workoutDay != null &&
          todaysWorkoutData.workoutDay!.name.isNotEmpty) {
        // Try to get the name from the workout day object
        workoutName = todaysWorkoutData.workoutDay!.name;
        Logger.meal(
            'Using workout day name from workout day object: $workoutName',
            tag: 'HomeScreen');
      }
    }

    if (hasWorkout && todaysWorkoutData != null) {
      // Prioritize data from the first workout log if available
      if (hasWorkoutLogs) {
        firstWorkoutLog = todaysWorkoutData.workoutLogs.first;
        workoutDay = firstWorkoutLog.workoutDay;

        // Set the workout name from the workout day if available
        if (workoutDay != null && workoutDay.name.isNotEmpty) {
          workoutName = workoutDay.name;
          Logger.meal('Using workout name from workout log: $workoutName',
              tag: 'HomeScreen');
        }
      }
      // If no logs, but sessions exist, try to get WorkoutDay data from the response structure
      // (assuming the API might provide workout_day details even without logs)
      else if (hasSessions) {
        // Check if workoutDayData is available directly in TodaysWorkoutResponse
        // (This depends on how TodaysWorkoutResponse is structured and populated by ApiService)
        // If TodaysWorkoutResponse has a direct WorkoutDay property for the current day:
        // workoutDay = todaysWorkoutData.workoutDayData; // Hypothetical property

        // If not directly available, we might need to infer from sessions or rely on defaults.
        // For simplicity, let's assume WorkoutDay details are primarily tied to logs.
        // If we only have sessions, we might lack the specific WorkoutDay name/details.
        // We can try to get the cover image from the top level if available.
        coverImageUrl = todaysWorkoutData.coverImageUrl;
        Logger.debug('Top level coverImageUrl: $coverImageUrl',
            tag: 'HomeScreen');
        Logger.debug(
            'workoutPlanCoverImageUrl: ${todaysWorkoutData.workoutPlanCoverImageUrl}',
            tag: 'HomeScreen');

        // If coverImageUrl is null, try using workoutPlanCoverImageUrl as fallback
        if (coverImageUrl == null || coverImageUrl.isEmpty) {
          coverImageUrl = todaysWorkoutData.workoutPlanCoverImageUrl;
          Logger.debug(
              'Using workoutPlanCoverImageUrl as fallback: $coverImageUrl',
              tag: 'HomeScreen');
        }

        // If still null, use a default workout image URL
        if (coverImageUrl == null || coverImageUrl.isEmpty) {
          coverImageUrl =
              'https://acfit-media.s3.amazonaws.com/workout_days/covers/default_workout.jpg';
          Logger.debug('Using default workout image: $coverImageUrl',
              tag: 'HomeScreen');
        }

        // We need to calculate duration/calories from sessions since workoutDay is null
        duration = todaysWorkoutData.sessions!
            .fold<int>(0, (sum, session) => sum + (session.duration ?? 0));
        calories = todaysWorkoutData.sessions!.fold<int>(
            0, (sum, session) => sum + (session.caloriesBurnEstimate ?? 0));
        Logger.debug(
            'Calculated from sessions - duration: $duration, calories: $calories',
            tag: 'HomeScreen');
      }

      // Extract details from the WorkoutDay object if we have one
      if (workoutDay != null) {
        Logger.debug('WorkoutDay object available: ${workoutDay.id}',
            tag: 'HomeScreen');
        Logger.debug('WorkoutDay coverImageUrl: ${workoutDay.coverImageUrl}',
            tag: 'HomeScreen');

        // Set the workout name
        if (workoutDay.name.isNotEmpty) {
          workoutName = workoutDay.name;
          Logger.debug('Using workout name from WorkoutDay: $workoutName',
              tag: 'HomeScreen');
        } else {
          Logger.debug('WorkoutDay name is empty', tag: 'HomeScreen');
        }

        // Set other workout details
        duration = workoutDay.totalDuration! > 0
            ? workoutDay.totalDuration
            : duration; // Add null check
        calories = workoutDay.caloriesBurnEstimate! > 0 // Add null check
            ? workoutDay.caloriesBurnEstimate
            : calories;

        // Prioritize workoutDay image if available
        if (workoutDay.coverImageUrl != null &&
            workoutDay.coverImageUrl!.isNotEmpty) {
          coverImageUrl = workoutDay.coverImageUrl;
          Logger.debug('Using coverImageUrl from WorkoutDay: $coverImageUrl',
              tag: 'HomeScreen');
        } else if (todaysWorkoutData.coverImageUrl != null &&
            todaysWorkoutData.coverImageUrl!.isNotEmpty) {
          // If workoutDay doesn't have a cover image, try using the top-level one
          coverImageUrl = todaysWorkoutData.coverImageUrl;
          Logger.debug(
              'Using top-level coverImageUrl as fallback: $coverImageUrl',
              tag: 'HomeScreen');
        } else if (todaysWorkoutData.workoutPlanCoverImageUrl != null &&
            todaysWorkoutData.workoutPlanCoverImageUrl!.isNotEmpty) {
          // If that's also missing, try the workout plan cover image
          coverImageUrl = todaysWorkoutData.workoutPlanCoverImageUrl;
          Logger.debug(
              'Using workoutPlanCoverImageUrl as fallback: $coverImageUrl',
              tag: 'HomeScreen');
        } else {
          // If all else fails, use a default image
          coverImageUrl =
              'https://acfit-media.s3.amazonaws.com/workout_days/covers/default_workout.jpg';
          Logger.debug('Using default workout image: $coverImageUrl',
              tag: 'HomeScreen');
        }

        // Removed debug logging
      } else {
        // Removed debug logging

        // Check if the API response includes a workout day name directly
        if (todaysWorkoutData.workoutDayName != null &&
            todaysWorkoutData.workoutDayName!.trim().isNotEmpty) {
          workoutName = todaysWorkoutData.workoutDayName!;
          // Removed debug logging
        }
      }

      // --- Completion Status Check ---
      // Check if all *expected* sessions for the day have a completed log entry
      final List<WorkoutSession>? sessions = todaysWorkoutData.sessions;

      // Check if sessions are completed by looking at session logs
      if (sessions != null && sessions.isNotEmpty) {
        final Set<int> completedSessionIds = {};
        // Check session logs for completed sessions
        for (final log in todaysWorkoutData.workoutLogs) {
          if (log.sessionLogs != null) {
            for (final sessionLog in log.sessionLogs!) {
              if (sessionLog.isCompleted) {
                completedSessionIds.add(sessionLog.workoutSessionId);
                Logger.workout(
                    'Found completed session log for session ${sessionLog.workoutSessionId}',
                    tag: 'HomeScreen');
              }
            }
          }
        }
        // Check if the count of completed unique session IDs matches the total number of sessions for the day
        isTodayWorkoutCompleted = completedSessionIds.length >= sessions.length;
        Logger.workout(
            'Checking session completion: ${completedSessionIds.length}/${sessions.length} sessions completed',
            tag: 'HomeScreen');
      } else if (hasWorkoutLogs && firstWorkoutLog != null) {
        // Fallback: If no sessions list, check the completion status of the first log
        // This might be inaccurate if a day has multiple sessions but only one log is created initially
        isTodayWorkoutCompleted = firstWorkoutLog.isCompleted;
        Logger.workout(
            'Using first workout log completion status: ${firstWorkoutLog.isCompleted}',
            tag: 'HomeScreen');
      } else {
        // If no logs and no sessions, it cannot be completed
        isTodayWorkoutCompleted = false;
        Logger.workout('No logs or sessions found, workout cannot be completed',
            tag: 'HomeScreen');
      }
    } else {
      // Removed debug logging
    }

    // Only set defaults if we actually have a workout
    if (hasWorkout) {
      // Ensure duration and calories have sensible defaults if still null/zero
      duration = (duration == null || duration <= 0) ? 30 : duration;
      calories = (calories == null || calories <= 0) ? 300 : calories;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Workouts',
                style: TextStyle(
                  fontSize: 18, // Larger title
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF101114),
                  fontFamily: 'Work Sans',
                ),
              ),
              TextButton(
                onPressed: () => NavigationService.navigateToReplacementNamed(
                  NavigationService.main,
                  arguments: {'initialTabIndex': 1},
                ),
                child: const Text(
                  'See All',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFFF97316),
                    fontFamily: 'Work Sans',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Always show the workout widget, regardless of completion status
          if (hasWorkout)
            // Use the new WorkoutFrameWidget
            WorkoutFrameWidget(
              coverImageUrl:
                  coverImageUrl, // Use the determined cover image URL
              workoutName: workoutName, // Use the determined workout name
              duration: duration, // Use the determined duration
              calories: calories, // Use the determined calories
              isCompleted:
                  isTodayWorkoutCompleted, // Pass the determined completion status
              onTap: () {
                // Navigate to the main screen with workout tab selected
                NavigationService.navigateToReplacementNamed(
                  NavigationService.main,
                  arguments: {'initialTabIndex': 1},
                );
              },
            ),
          // If no workout is available, show a message
          if (!hasWorkout)
            Container(
              width: double.infinity,
              height: 200,
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: const Color(0xFF1A1B1F),
              ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.fitness_center,
                    color: Colors.white54,
                    size: 48,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No workout scheduled for today',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                      fontFamily: 'Work Sans',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNutritionSection() {
    // Use the TodaysMealResponse model - Check if mealLogs itself is null
    final List<meal_log_models.MealLog> meals = _mealData?.mealLogs ?? [];
    final bool hasMeals = meals.isNotEmpty;
    Logger.debug(
        'MEALS: Building nutrition section. Has meals: $hasMeals. Count: ${meals.length}',
        tag: 'HomeScreen'); // Log meal count for UI
    if (hasMeals) {
      Logger.debug(
          'MEALS: Building nutrition section - First meal: ${meals.first.meal.name}',
          tag: 'HomeScreen');
    }
    // Building Nutrition Section. Has Meals: $hasMeals

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Diet & Nutrition',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF101114),
                  fontFamily: 'Work Sans',
                ),
              ),
              TextButton(
                onPressed: () => NavigationService.navigateToNamed(
                    '/meal-plan'), // Use constant
                child: const Text(
                  'See All',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFFF97316),
                    fontFamily: 'Work Sans',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16), // Increased spacing
          if (hasMeals)
            Padding(
              padding: const EdgeInsets.only(
                  bottom: 16, top: 4), // Added top padding for better spacing
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  // Display ALL meals fetched for the day
                  children: meals
                      // .take(2) // REMOVE .take(2) to show all meals
                      .map((mealLog) {
                        // Date is handled by the MealCardWidget

                        return _buildMealCard(mealLog);
                      })
                      .toList()
                      .expand((widget) => [
                            widget,
                            const SizedBox(width: 16)
                          ]) // Increased spacing between cards
                      .toList()
                    ..removeLast(), // Remove last spacer
                ),
              ),
            )
          else
            // No meals placeholder with consistent styling
            GestureDetector(
              onTap: () => NavigationService.navigateToNamed('/meal-plan'),
              child: Container(
                width: double.infinity,
                height: 200,
                padding:
                    const EdgeInsets.symmetric(vertical: 40, horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  color: const Color(0xFF1A1B1F),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.restaurant,
                      color: Colors.white54,
                      size: 48,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No meals scheduled for today',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        fontFamily: 'Work Sans',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build individual meal card (simplified)
  Widget _buildMealCard(meal_log_models.MealLog mealLog) {
    // Parse the date string into a DateTime object
    final DateTime? dateObject =
        _mealData?.date != null ? DateTime.tryParse(_mealData!.date) : null;

    // Create a copy of the mealLog with isCompletable set to true for today's meals
    // This ensures the complete button will show in meal details screen
    final updatedMealLog = meal_log_models.MealLog(
      id: mealLog.id,
      meal: mealLog.meal,
      mealType: mealLog.mealType,
      isCompleted: mealLog.isCompleted,
      isCompletable: true, // Force to true for today's meals in home screen
      completionTime: mealLog.completionTime,
      actualCalories: mealLog.actualCalories,
      actualProtein: mealLog.actualProtein,
      actualCarbs: mealLog.actualCarbs,
      actualFat: mealLog.actualFat,
      originalMealLogId: mealLog.originalMealLogId,
      scheduledTime: mealLog.scheduledTime,
    );

    return MealCardWidget(
      mealLog:
          updatedMealLog, // Use the updated meal log with isCompletable=true
      // Pass the parsed DateTime object
      date: dateObject,
      // Add onMealCompleted callback to refresh data
      onMealCompleted: () async {
        Logger.info(
            'Meal completed callback triggered from home screen. Refreshing data...',
            tag: 'HomeScreen');

        // First refresh calorie data specifically to ensure it's up to date
        try {
          await _apiService.getCalories(skipCache: true);
          Logger.info(
              'Successfully refreshed calorie data from home screen callback',
              tag: 'HomeScreen');
        } catch (e) {
          Logger.error('Failed to refresh calorie data from home screen: $e',
              tag: 'HomeScreen');
        }

        // Then refresh all data
        _loadData(forceRefresh: true);
      },
    );
  }

  // OLD Helper for nutrient chips in meal card (replaced by _buildNutrientBox)
  /*
  Widget _buildNutrientChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: 10, vertical: 6), // Adjusted padding
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF101114),
              fontFamily: 'Work Sans',
            ),
          ),
          const SizedBox(height: 2), // Reduced spacing
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              color: Color(0xFF676B74), // Softer text color
              fontFamily: 'Work Sans',
            ),
          ),
        ],
      ),
    );
  }
  */

  String _getMonthName(int month) {
    // Ensure month is within valid range
    if (month < 1 || month > 12) {
      return '';
    }
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }
} // End of _HomeScreenState class

// Ensure ScoreRingPainter is defined *outside* _HomeScreenState
class ScoreRingPainter extends CustomPainter {
  final double progress;
  final Color color;

  ScoreRingPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width / 2, size.height / 2) -
        2; // Adjust radius slightly for padding
    const strokeWidth = 4.0; // Keep stroke width constant

    // Draw background circle
    final backgroundPaint = Paint()
      ..color = color.withAlpha(51) // Use withAlpha
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round; // Round ends for smoother look

    // Calculate sweep angle based on progress
    final sweepAngle = progress * 2 * math.pi; // Use math.pi

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start angle at the top (-90 degrees)
      sweepAngle,
      false, // Draw arc, not segment
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant ScoreRingPainter oldDelegate) {
    // Only repaint if progress or color changes
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
