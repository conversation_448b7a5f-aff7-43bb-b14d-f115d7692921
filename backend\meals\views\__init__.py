# Import ViewSets from their respective modules
from .meal import (
    MealViewSet, # Removed MealIngredientViewSet
    MealCategoryViewSet, MealVarietyViewSet, MealTimingViewSet,
    MealSubstitutionViewSet, MealLogViewSet, MealReminderViewSet
)
from .plan import (
    MealPlanViewSet, DailyMealPlanViewSet, MealScheduleViewSet,
    UserMealPlanViewSet # Removed MealReminderViewSet, GroceryListViewSet, GroceryItemViewSet. Added UserMealPlanViewSet
)
from .hydration import (
    HydrationLogViewSet, get_todays_hydration, update_hydration,
    get_hydration_history
)
# FIX: Correct import location for GroceryListViewSet and GroceryItemViewSet
from .nutrition import (
    # Removed GroceryListViewSet, GroceryItemViewSet
    # Removed MealProgressViewSet
    get_nutrition_progress,
    get_meal_plan_adherence
)

# Don't use importlib, directly import from rest_framework
from rest_framework import viewsets
ViewSet = viewsets.ViewSet
ModelViewSet = viewsets.ModelViewSet

# Import and define the API functions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from ..models import UserMealPlan, DailyMealPlan, MealSchedule, MealLog, Meal, HydrationLog # Import HydrationLog
from ..serializers import DailyMealPlanSerializer, MealSerializer # Import MealSerializer
from accounts.models import UserProfile, UserProgress
import logging
import pytz
from datetime import datetime, timedelta, date

logger = logging.getLogger(__name__) # Define logger

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_todays_meals(request):
    """Get today's meals for the logged-in user based on their current day in the program"""
    try:
        user_profile = UserProfile.objects.get(user=request.user)

        # Get the current active meal plan
        user_meal_plan = UserMealPlan.objects.filter(
            user=user_profile,
            is_active=True,
            start_date__lte=timezone.now().date()
        ).first()

        if not user_meal_plan:
            # No active plan found
            logger.info(f"No active meal plan found for user {user_profile.id}")
            return Response({
                "date": timezone.now().date().strftime("%Y-%m-%d"),
                "meal_logs": [],
                "no_plan_assigned": True,
                "message": "No active meal plan found."
            }, status=200) # Return 200 with empty data

        # Calculate the current day number in the program
        current_day = user_meal_plan.get_current_day()
        logger.info(f"[get_todays_meals] Calculated current_day based on UserMealPlan.start_date: {current_day}")
        day_of_program = current_day

        if day_of_program <= 0:
             return Response({"message": "Meal plan has not started yet"}, status=404)

        # Calculate week number and day name (0=Monday)
        week_number = (day_of_program - 1) // 7 + 1
        day_index = (day_of_program - 1) % 7
        day_names = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        day_field_name = day_names[day_index]

        # Fetch the user's specific schedule for the current week
        user_schedule = MealSchedule.objects.filter(
            user_meal_plan=user_meal_plan,
            week_number=week_number
        ).first()

        if not user_schedule:
             # Optional: Fallback to global template if user schedule doesn't exist?
             # global_schedule = MealSchedule.objects.filter(meal_plan=user_meal_plan.meal_plan, week_number=week_number, user_meal_plan__isnull=True).first()
             # if not global_schedule:
             #     return Response({"message": "No schedule found for this week"}, status=404)
             # daily_meal_plan = getattr(global_schedule, day_field_name, None)
             # logger.warning(f"Using global meal schedule fallback for UserMealPlan {user_meal_plan.id}, Week {week_number}")
             # For now, strictly require user schedule:
             return Response({"message": "User-specific meal schedule not found for this week"}, status=404)


        # Get the DailyMealPlan assigned for today on the user's schedule
        daily_meal_plan = getattr(user_schedule, day_field_name, None)

        if not daily_meal_plan:
            logger.info(f"No DailyMealPlan found for today for UserMealPlan {user_meal_plan.id} via get_todays_meals")
            return Response([], status=200) # Return empty list if no plan for today

        # --- Start: Added logic to check completion status ---
        today_date = timezone.now().date()
        meals_for_today = []
        meal_ids_to_check = []

        # Collect meals and their types from the daily plan
        scheduled_meals = {
            'breakfast': daily_meal_plan.breakfast, # Corrected variable name
            'lunch': daily_meal_plan.lunch,       # Corrected variable name
            'dinner': daily_meal_plan.dinner        # Corrected variable name
        }

        for meal_type, meal_instance in scheduled_meals.items():
            if meal_instance:
                meals_for_today.append({
                    'meal_type': meal_type,
                    'meal': meal_instance,
                    'is_completed': False # Default to not completed
                })
                meal_ids_to_check.append(meal_instance.id)

        # Query MealLog for *any* log entry for these meals today
        meal_log_map = {} # Store meal_id -> meal_log_id
        completed_meal_ids = set() # Store meal_ids that are completed

        if meal_ids_to_check:
            # Fetch existing logs for today, getting both log ID and meal ID
            existing_logs_today = MealLog.objects.filter(
                user=user_profile,
                meal_id__in=meal_ids_to_check,
                date=today_date
            ).values('id', 'meal_id', 'is_completed') # Fetch id, meal_id, and completion status

            for log in existing_logs_today:
                meal_log_map[log['meal_id']] = log['id'] # Map meal_id to its log_id for today
                if log['is_completed']:
                    completed_meal_ids.add(log['meal_id']) # Add to completed set if applicable

            logger.debug(f"Meal log map for {user_profile.user.email} on {today_date}: {meal_log_map}")
            logger.debug(f"Completed meal IDs for {user_profile.user.email} on {today_date}: {completed_meal_ids}")


        # Serialize the result including completion status and meal_log_id
        result_data = []
        for meal_info in meals_for_today:
            meal_id = meal_info['meal'].id
            is_completed = meal_id in completed_meal_ids
            meal_log_id = meal_log_map.get(meal_id) # Get log_id if exists, else None

            # Use MealSerializer to get full meal details
            meal_serializer = MealSerializer(meal_info['meal'], context={'request': request})
            result_data.append({
                'meal_type': meal_info['meal_type'],
                'meal': {
                    'id': meal_info['meal'].id,  # Explicitly include the meal ID
                    'name': meal_info['meal'].name,
                    'description': meal_info['meal'].description,
                    'calories': meal_info['meal'].calories,
                    'protein': meal_info['meal'].protein,
                    'carbs': meal_info['meal'].carbs,
                    'fat': meal_info['meal'].fat,
                    'image_url': request.build_absolute_uri(meal_info['meal'].meal_image.url) if meal_info['meal'].meal_image else meal_info['meal'].image_url,
                },
                'meal_details': meal_serializer.data,
                'is_completed': is_completed,
                'meal_log_id': meal_log_id # Add the actual meal_log_id or null
            })
        # --- End: Modified logic ---

        # Get hydration data for today
        try:
            logger.info(f"[get_todays_meals] Fetching HydrationLog for user {user_profile.id} and date {today_date}")
            hydration_log = HydrationLog.objects.filter(
                user=user_profile,
                date=today_date
            ).first()
            db_hydration_amount = hydration_log.amount_ml if hydration_log else 'None (Log Not Found)'
            logger.info(f"[get_todays_meals] Hydration amount read from DB: {db_hydration_amount}")

            hydration_data = {
                'amount_ml': hydration_log.amount_ml if hydration_log else 0,
                'target_ml': hydration_log.target_ml if hydration_log else 2500
            }
            logger.info(f"Hydration data for {user_profile.user.email} on {today_date}: {hydration_data}")
        except Exception as e:
            logger.error(f"Error getting hydration data: {str(e)}")
            hydration_data = {
                'amount_ml': 0,
                'target_ml': 2500
            }

        # Get calorie data for today
        try:
            # Calculate calories consumed from completed meals
            calories_consumed = 0
            for meal_id in completed_meal_ids:
                meal = Meal.objects.get(id=meal_id)
                calories_consumed += meal.calories or 0

            # Get user's daily calorie target (could be from profile or calculated)
            calorie_target = 2200  # Default value
            if hasattr(user_profile, 'daily_calorie_target') and user_profile.daily_calorie_target:
                calorie_target = user_profile.daily_calorie_target

            calorie_data = {
                'consumed': calories_consumed,
                'target': calorie_target,
                'remaining': calorie_target - calories_consumed
            }
            logger.info(f"Calorie data for {user_profile.user.email} on {today_date}: {calorie_data}")
        except Exception as e:
            logger.error(f"Error calculating calorie data: {str(e)}")
            calorie_data = {
                'consumed': 0,
                'target': 2200,
                'remaining': 2200
            }

        # Return the structured data with day information, hydration, and calories
        today_date_str = timezone.now().date().strftime("%Y-%m-%d")
        return Response({
            "date": today_date_str,
            "day_number": day_of_program,
            "meal_logs": result_data,
            "hydration": hydration_data,
            "calories": calorie_data,
            "message": f"Meals for day {day_of_program}"
        })

    except UserProfile.DoesNotExist:
         logger.error(f"UserProfile not found for user {request.user.id} in get_todays_meals")
         return Response({"error": "User profile not found."}, status=404)
    except Exception as e:
        logger.error(f"Error in get_todays_meals for user {request.user.id}: {str(e)}", exc_info=True)
        return Response({"error": str(e)}, status=500)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_meals_by_program_day(request):
    """Get meals for a specific program day number or current day if not specified"""
    try:
        user_profile = UserProfile.objects.get(user=request.user)

        # --- Get User's Local Timezone and Date ---
        try:
            user_tz = pytz.timezone(user_profile.timezone)
        except pytz.UnknownTimeZoneError:
            logger.warning(f"Invalid timezone '{user_profile.timezone}' for user {request.user.id}. Using UTC.")
            user_tz = pytz.utc
        now_local = timezone.now().astimezone(user_tz)
        today_local = now_local.date()
        # ---

        # Get the day number from the request parameters or calculate current day
        day_param = request.query_params.get('day')

        # Get the date parameter from the request (optional)
        date_param = request.query_params.get('date')
        if date_param:
            try:
                # Parse the date parameter
                target_date = datetime.strptime(date_param, '%Y-%m-%d').date()
                logger.info(f"Using target date from parameter: {target_date}")
            except ValueError:
                # If date parameter is invalid, use today's date
                target_date = today_local
                logger.warning(f"Invalid date parameter: {date_param}, using today's date: {target_date}")
        else:
            # If no date parameter is provided, use today's date
            target_date = today_local
            logger.info(f"No date parameter provided, using today's date: {target_date}")

        # Get the current active meal plan
        user_meal_plan = UserMealPlan.objects.filter(
            user=user_profile,
            is_active=True,
            start_date__lte=today_local
        ).first()

        if not user_meal_plan:
            # No active plan found
            return Response({
                "date": today_local.strftime("%Y-%m-%d"),
                "meal_logs": [],
                "no_plan_assigned": True,
                "message": "No active meal plan found."
            }, status=200) # Return 200 with empty data

        # If day parameter is provided, use it; otherwise calculate current day
        if day_param:
            try:
                day_number = int(day_param)
            except ValueError:
                return Response({"error": "day parameter must be an integer"}, status=400)
        else:
            # Calculate current day based on plan start date
            if user_meal_plan.start_date:
                day_number = (today_local - user_meal_plan.start_date).days + 1
                if day_number <= 0:
                    return Response({"message": "Meal plan has not started yet"}, status=200)
            else:
                # Fallback to user progress if no start date
                progress, _ = UserProgress.objects.get_or_create(user=user_profile)
                day_number = progress.meal_day

        # Get the current active meal plan
        user_meal_plan = UserMealPlan.objects.filter(
            user=user_profile,
            is_active=True,
            start_date__lte=timezone.now().date()
        ).first()

        if not user_meal_plan:
            # No active plan found
            logger.info(f"No active meal plan found for user {user_profile.id}")
            return Response({
                "date": timezone.now().date().strftime("%Y-%m-%d"),
                "meal_logs": [],
                "no_plan_assigned": True,
                "message": "No active meal plan found."
            }, status=200) # Return 200 with empty data

        # Calculate week number and day name (0=Monday)
        week_number = (day_number - 1) // 7 + 1
        day_index = (day_number - 1) % 7
        day_names = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        day_field_name = day_names[day_index]

        logger.info(f"Fetching meals for user {user_profile.id}, program day {day_number} (week {week_number})")
        print(f"MEAL DEBUG: Fetching meals for user {user_profile.id}, program day {day_number} (week {week_number})")

        # Fetch the user's specific schedule for the requested week
        user_schedule = MealSchedule.objects.filter(
            user_meal_plan=user_meal_plan,
            week_number=week_number
        ).first()

        print(f"MEAL DEBUG: Looking for schedule - user_meal_plan={user_meal_plan.id}, week_number={week_number}")

        # If no user schedule, try to find any schedule for this meal plan and week
        if not user_schedule:
            print(f"MEAL DEBUG: No user-specific schedule found for week {week_number}, checking for global schedule")
            # Try to find a global schedule for this meal plan and week
            global_schedule = MealSchedule.objects.filter(
                meal_plan=user_meal_plan.meal_plan,
                week_number=week_number
            ).first()

            if global_schedule:
                print(f"MEAL DEBUG: Found global schedule for week {week_number}")
                user_schedule = global_schedule
            else:
                print(f"MEAL DEBUG: No schedule found for week {week_number}")
                return Response({"message": f"No meal schedule found for week {week_number}"}, status=404)

        # Get the DailyMealPlan assigned for the requested day using the helper method
        print(f"MEAL DEBUG: Looking for daily meal plan for day {day_number}")
        print(f"MEAL DEBUG: Schedule days field: {user_schedule.days}")

        # Use the helper method to get the daily meal plan
        daily_meal_plan = user_schedule.get_daily_plan_for_day_number(day_number)

        # Check if this is a future day (beyond the current day)
        current_day = user_meal_plan.get_current_day()
        is_future_day = day_number > current_day

        # If we can't find a plan for the requested day, handle it differently based on whether it's a future day
        if not daily_meal_plan:
            if is_future_day:
                print(f"MEAL DEBUG: No plan found for future day {day_number} (current day: {current_day})")
                # For future days, return a special response indicating it's a future day
                return Response({
                    "date": timezone.now().date().strftime("%Y-%m-%d"),
                    "day_number": day_number,
                    "meal_logs": [],
                    "hydration": {
                        'amount_ml': 0,
                        'target_ml': 2500
                    },
                    "is_future_day": True,
                    "message": f"Meals for day {day_number} will be available soon"
                }, status=200)
            elif day_number > 1:
                print(f"MEAL DEBUG: No plan found for day {day_number}, trying to find plan for day 1 as fallback")
                # Try to find a plan for day 1
                day_1_plan = user_schedule.get_daily_plan_for_day_number(1)
                if day_1_plan:
                    print(f"MEAL DEBUG: Found plan for day 1 (ID: {day_1_plan.id}) as fallback")
                    daily_meal_plan = day_1_plan
                    # Note: We'll still return the original day_number in the response

        if daily_meal_plan:
            print(f"MEAL DEBUG: Found daily meal plan: {daily_meal_plan.id} for day {day_number}")
        else:
            print(f"MEAL DEBUG: No daily meal plan found for day {day_number}")
            logger.info(f"No DailyMealPlan found for day {day_number} for UserMealPlan {user_meal_plan.id}")
            return Response({
                "date": timezone.now().date().strftime("%Y-%m-%d"),
                "day_number": day_number,
                "meal_logs": [],
                "message": f"No meals scheduled for day {day_number}"
            }, status=200) # Return empty list if no plan for the day

        # Check if the requested day is today
        today_date = timezone.now().date()
        current_day = user_meal_plan.get_current_day()
        is_today = (day_number == current_day)

        # Prepare meals data
        meals_for_day = []
        meal_ids_to_check = []

        # Collect meals and their types from the daily plan
        scheduled_meals = {
            'breakfast': daily_meal_plan.breakfast,
            'lunch': daily_meal_plan.lunch,
            'dinner': daily_meal_plan.dinner
        }

        for meal_type, meal_instance in scheduled_meals.items():
            if meal_instance:
                meals_for_day.append({
                    'meal_type': meal_type,
                    'meal': meal_instance,
                    'is_completed': False # Default to not completed
                })
                meal_ids_to_check.append(meal_instance.id)

        # For today's meals, check completion status
        meal_log_map = {}
        completed_meal_ids = set()

        if is_today and meal_ids_to_check:
            # Fetch existing logs for today
            existing_logs_today = MealLog.objects.filter(
                user=user_profile,
                meal_id__in=meal_ids_to_check,
                date=today_date
            ).values('id', 'meal_id', 'is_completed')

            for log in existing_logs_today:
                meal_log_map[log['meal_id']] = log['id']
                if log['is_completed']:
                    completed_meal_ids.add(log['meal_id'])

        # Serialize the result
        result_data = []
        for meal_info in meals_for_day:
            meal_id = meal_info['meal'].id
            is_completed = meal_id in completed_meal_ids
            meal_log_id = meal_log_map.get(meal_id)

            # Use MealSerializer to get full meal details
            meal_serializer = MealSerializer(meal_info['meal'], context={'request': request})
            result_data.append({
                'meal_type': meal_info['meal_type'],
                'meal': {
                    'id': meal_info['meal'].id,
                    'name': meal_info['meal'].name,
                    'description': meal_info['meal'].description,
                    'calories': meal_info['meal'].calories,
                    'protein': meal_info['meal'].protein,
                    'carbs': meal_info['meal'].carbs,
                    'fat': meal_info['meal'].fat,
                    'image_url': request.build_absolute_uri(meal_info['meal'].meal_image.url) if meal_info['meal'].meal_image else None,  # Use meal_image as image_url for backward compatibility
                },
                'meal_details': meal_serializer.data,
                'is_completed': is_completed,
                'is_completable': is_today,  # Only today's meals are completable
                'meal_log_id': meal_log_id
            })

        # Get hydration data for today (only relevant if the requested day is today)
        hydration_data = {
            'amount_ml': 0,
            'target_ml': 2500
        }

        if is_today:
            try:
                hydration_log = HydrationLog.objects.filter(
                    user=user_profile,
                    date=today_date
                ).first()

                if hydration_log:
                    hydration_data = {
                        'amount_ml': hydration_log.amount_ml,
                        'target_ml': hydration_log.target_ml
                    }
            except Exception as e:
                logger.error(f"Error getting hydration data: {str(e)}")

        # Check if this is a past, current, or future date
        today = timezone.now().date()
        is_past_date = target_date < today
        is_future_date = target_date > today

        # Return the structured data
        return Response({
            "date": target_date.strftime("%Y-%m-%d"),
            "day_number": day_number,
            "meal_logs": result_data,
            "hydration": hydration_data,
            "is_past_date": is_past_date,
            "is_future_date": is_future_date,
            "message": f"Meals for day {day_number}"
        })

    except UserProfile.DoesNotExist:
        logger.error(f"UserProfile not found for user {request.user.id} in get_meals_by_program_day")
        return Response({"error": "User profile not found."}, status=404)
    except Exception as e:
        logger.error(f"Error in get_meals_by_program_day for user {request.user.id}: {str(e)}", exc_info=True)
        return Response({"error": str(e)}, status=500)

__all__ = [
    'MealViewSet',
    # Removed 'MealIngredientViewSet',
    # Removed 'MealTemplateViewSet',
    'MealCategoryViewSet',
    'MealVarietyViewSet',
    'MealTimingViewSet',
    'MealSubstitutionViewSet',
    'MealLogViewSet',
    'MealPlanViewSet',
    'DailyMealPlanViewSet',
    'MealScheduleViewSet',
    'MealReminderViewSet', # Keep this export
    # Removed 'GroceryListViewSet',
    # Removed 'GroceryItemViewSet',
    'HydrationLogViewSet',
    'UserMealPlanViewSet', # Add export for UserMealPlanViewSet
    # Removed 'MealProgressViewSet',
    'get_todays_hydration',
    'update_hydration',
    'get_hydration_history',
    'get_nutrition_progress',
    'get_meal_plan_adherence',
    'get_todays_meals',
    'get_meals_by_program_day',
]
